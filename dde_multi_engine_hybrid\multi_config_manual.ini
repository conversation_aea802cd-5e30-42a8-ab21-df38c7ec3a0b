# DDE Multi-Product Monitor Configuration v7.1_multi_engine_hybrid
# 多商品DDE監控系統配置檔案
# 基於模組化版本改造，支持多商品監控
#
# python dde_monitor_multi_engine.py -c multi_config_manual.ini
#

# ===== 系統設定 =====
[System]
dde_service = XQTISC
dde_topic = Quote
disconnect_on_exit = false
output_base_path = ./data/
log_base_path = ./logs/manual/

# ===== DDE性能設定 =====
[Common_Performance]
# DDE請求間隔 (秒) - 增加間隔避免DDE過載導致崩潰
request_interval = 0.001
# DDE訂閱間隔 (秒) - 增加間隔避免DDE過載導致崩潰
subscribe_interval = 0.001
# 批次處理大小 - 減少批次大小避免過載
batch_size = 500
# 启用并行处理 - 暂时禁用
enable_parallel_processing = false

# ===== 自動連線控制設定 =====
[AutoConnection]
# 商品間連線間隔 (秒) - 增加間隔避免同時連線造成DDE瓶頸
symbol_connection_interval = 0.5
# 最大並發連線數 - 限制同時連線的商品數量
max_concurrent_connections = 1
# 連線失敗重試次數
connection_retry_attempts = 3
# 連線失敗重試間隔 (秒)
connection_retry_interval = 8.0
# 連線超時時間 (秒)
connection_timeout = 45.0

# ===== 商品清單 =====
[Symbols]
# 啟用的商品清單 (期貨 + 股票範例)
#symbol_list = FITXN07.TF,FITX07.TF,FITXN08.TF,FIMTXN07.TF,FITMN07.TF,2330.TW,2317.TW,TXoN07C22500.TF,TXoN07P22500.TF
#symbol_list = FITXN07.TF,FIMTXN07.TF,FITMN07.TF,FITXN08.TF,FIMTXN08.TF,FITMN08.TF,FITX07.TF,FIMTX07.TF,FITM07.TF,FITX08.TF,FIMTX08.TF,FITM08.TF,2330.TW,2317.TW,TXoN07C22500.TF,TXoN07P22500.TF
# 減少商品數量以降低DDE負載，避免崩潰
#symbol_list = FITXN07.TF,FIMTXN07.TF,FITMN07.TF,FITXN08.TF,FIMTXN08.TF,FITMN08.TF,FITX07.TF,FIMTX07.TF,FITM07.TF,FITX08.TF,FIMTX08.TF,FITM08.TF,2330.TW,2317.TW,TXoN07C22000.TF,TXoN07C22100.TF,TXoN07C22200.TF,TXoN07C22300.TF,TXoN07C22400.TF,TXoN07C22500.TF,TXoN07C22600.TF,TXoN07C22700.TF,TXoN07C22800.TF,TXoN07C22900.TF,TXoN07C23000.TF,TXoN07C23100.TF,TXoN07C23200.TF,TXoN07C23300.TF,TXoN07C23400.TF,TXoN07C23500.TF,TXoN07C23600.TF,TXoN07C23700.TF,TXoN07C23800.TF,TXoN07C23900.TF,TXoN07P22000.TF,TXoN07P22100.TF,TXoN07P22200.TF,TXoN07P22300.TF,TXoN07P22400.TF,TXoN07P22500.TF,TXoN07P22600.TF,TXoN07P22700.TF,TXoN07P22800.TF,TXoN07P22900.TF,TXoN07P23000.TF,TXoN07P23100.TF,TXoN07P23200.TF,TXoN07P23300.TF,TXoN07P23400.TF,TXoN07P23500.TF,TXoN07P23600.TF,TXoN07P23700.TF,TXoN07P23800.TF,TXoN07P23900.TF
symbol_list = FITXN07.TF,FIMTXN07.TF,FITMN07.TF,FITXN08.TF,FIMTXN08.TF,FITMN08.TF,FITX07.TF,FIMTX07.TF,FITM07.TF,FITX08.TF,FIMTX08.TF,FITM08.TF,2330.TW,2317.TW
#,TXoN07C22000.TF,TXoN07C22100.TF,TXoN07C22200.TF,TXoN07C22300.TF,TXoN07C22400.TF,TXoN07C22500.TF,TXoN07C22600.TF,TXoN07C22700.TF,TXoN07C22800.TF,TXoN07C22900.TF,TXoN07C23000.TF,TXoN07C23100.TF,TXoN07C23200.TF,TXoN07C23300.TF,TXoN07C23400.TF,TXoN07C23500.TF,TXoN07C23600.TF,TXoN07C23700.TF,TXoN07C23800.TF,TXoN07C23900.TF,TXoN07C24000.TF,TXoN07C24100.TF,TXoN07C24200.TF,TXoN07C24300.TF,TXoN07C24400.TF,TXoN07C24500.TF,TXoN07C24600.TF,TXoN07C24700.TF,TXoN07C24800.TF,TXoN07C24900.TF,TXoN07C25000.TF,TXoN07C25100.TF,TXoN07C25200.TF,TXoN07C25300.TF,TXoN07C25400.TF,TXoN07C25500.TF,TXoN07C25600.TF,TXoN07C25700.TF,TXoN07C25800.TF,TXoN07C25900.TF,TXoN07P22000.TF,TXoN07P22100.TF,TXoN07P22200.TF,TXoN07P22300.TF,TXoN07P22400.TF,TXoN07P22500.TF,TXoN07P22600.TF,TXoN07P22700.TF,TXoN07P22800.TF,TXoN07P22900.TF,TXoN07P23000.TF,TXoN07P23100.TF,TXoN07P23200.TF,TXoN07P23300.TF,TXoN07P23400.TF,TXoN07P23500.TF,TXoN07P23600.TF,TXoN07P23700.TF,TXoN07P23800.TF,TXoN07P23900.TF,TXoN07P24000.TF,TXoN07P24100.TF,TXoN07P24200.TF,TXoN07P24300.TF,TXoN07P24400.TF,TXoN07P24500.TF,TXoN07P24600.TF,TXoN07P24700.TF,TXoN07P24800.TF,TXoN07P24900.TF,TXoN07P25000.TF,TXoN07P25100.TF,TXoN07P25200.TF,TXoN07P25300.TF,TXoN07P25400.TF,TXoN07P25500.TF,TXoN07P25600.TF,TXoN07P25700.TF,TXoN07P25800.TF,TXoN07P25900.TF

#FITXN07.TF,FIMTXN07.TF,FITMN07.TF,FITXN08.TF,FIMTXN08.TF,FITMN08.TF,FITX07.TF,FIMTX07.TF,FITM07.TF,FITX08.TF,FIMTX08.TF,FITM08.TF
#,2330.TW,2317.TW
#,TXoN07C22000.TF,TXoN07C22100.TF,TXoN07C22200.TF,TXoN07C22300.TF,TXoN07C22400.TF,TXoN07C22500.TF,TXoN07C22600.TF,TXoN07C22700.TF,TXoN07C22800.TF,TXoN07C22900.TF
#,TXoN07C23000.TF,TXoN07C23100.TF,TXoN07C23200.TF,TXoN07C23300.TF,TXoN07C23400.TF,TXoN07C23500.TF,TXoN07C23600.TF,TXoN07C23700.TF,TXoN07C23800.TF,TXoN07C23900.TF
#,TXoN07C24000.TF,TXoN07C24100.TF,TXoN07C24200.TF,TXoN07C24300.TF,TXoN07C24400.TF,TXoN07C24500.TF,TXoN07C24600.TF,TXoN07C24700.TF,TXoN07C24800.TF,TXoN07C24900.TF
#,TXoN07C25000.TF,TXoN07C25100.TF,TXoN07C25200.TF,TXoN07C25300.TF,TXoN07C25400.TF,TXoN07C25500.TF,TXoN07C25600.TF,TXoN07C25700.TF,TXoN07C25800.TF,TXoN07C25900.TF
#,TXoN07P22000.TF,TXoN07P22100.TF,TXoN07P22200.TF,TXoN07P22300.TF,TXoN07P22400.TF,TXoN07P22500.TF,TXoN07P22600.TF,TXoN07P22700.TF,TXoN07P22800.TF,TXoN07P22900.TF
#,TXoN07P23000.TF,TXoN07P23100.TF,TXoN07P23200.TF,TXoN07P23300.TF,TXoN07P23400.TF,TXoN07P23500.TF,TXoN07P23600.TF,TXoN07P23700.TF,TXoN07P23800.TF,TXoN07P23900.TF
#,TXoN07P24000.TF,TXoN07P24100.TF,TXoN07P24200.TF,TXoN07P24300.TF,TXoN07P24400.TF,TXoN07P24500.TF,TXoN07P24600.TF,TXoN07P24700.TF,TXoN07P24800.TF,TXoN07P24900.TF
#,TXoN07P25000.TF,TXoN07P25100.TF,TXoN07P25200.TF,TXoN07P25300.TF,TXoN07P25400.TF,TXoN07P25500.TF,TXoN07P25600.TF,TXoN07P25700.TF,TXoN07P25800.TF,TXoN07P25900.TF


# ===== 資料類型模板 =====
[Template_Tick_Items]
item1_name = 交易時間
item1_code = {symbol}-Time
item2_name = 交易日期
item2_code = {symbol}-TradingDate
item3_name = 開盤價
item3_code = {symbol}-Open
item4_name = 最高價
item4_code = {symbol}-High
item5_name = 最低價
item5_code = {symbol}-Low
item6_name = 成交價
item6_code = {symbol}-Price
item7_name = 總量
item7_code = {symbol}-TotalVolume
item8_name = 單量
item8_code = {symbol}-Volume

[Template_Order_Items]
item1_name = 累委買口
item1_code = {symbol}-NWTotalBidContract
item2_name = 累委賣口
item2_code = {symbol}-NWTotalAskContract
item3_name = 累委買筆
item3_code = {symbol}-NWTotalBidSize
item4_name = 累委賣筆
item4_code = {symbol}-NWTotalAskSize
item5_name = 內盤量
item5_code = {symbol}-InSize
item6_name = 外盤量
item6_code = {symbol}-OutSize
item7_name = 累買成筆
item7_code = {symbol}-TotalBidMatchTx
item8_name = 累賣成筆
item8_code = {symbol}-TotalAskMatchTx

[Template_Level2_Items]
item1_name = 第1買價
item1_code = {symbol}-BestBid1
item2_name = 第2買價
item2_code = {symbol}-BestBid2
item3_name = 第3買價
item3_code = {symbol}-BestBid3
item4_name = 第4買價
item4_code = {symbol}-BestBid4
item5_name = 第5買價
item5_code = {symbol}-BestBid5
item6_name = 第1賣價
item6_code = {symbol}-BestAsk1
item7_name = 第2賣價
item7_code = {symbol}-BestAsk2
item8_name = 第3賣價
item8_code = {symbol}-BestAsk3
item9_name = 第4賣價
item9_code = {symbol}-BestAsk4
item10_name = 第5賣價
item10_code = {symbol}-BestAsk5
item11_name = 第1買量
item11_code = {symbol}-BestBidSize1
item12_name = 第2買量
item12_code = {symbol}-BestBidSize2
item13_name = 第3買量
item13_code = {symbol}-BestBidSize3
item14_name = 第4買量
item14_code = {symbol}-BestBidSize4
item15_name = 第5買量
item15_code = {symbol}-BestBidSize5
item16_name = 第1賣量
item16_code = {symbol}-BestAskSize1
item17_name = 第2賣量
item17_code = {symbol}-BestAskSize2
item18_name = 第3賣量
item18_code = {symbol}-BestAskSize3
item19_name = 第4賣量
item19_code = {symbol}-BestAskSize4
item20_name = 第5賣量
item20_code = {symbol}-BestAskSize5

[Template_Daily_Items]
item1_name = 商品名稱
item1_code = {symbol}-Name
item2_name = 到期日
item2_code = {symbol}-WContractDate
item3_name = 結算價
item3_code = {symbol}-SettlePrice
item4_name = 漲停價
item4_code = {symbol}-UpLimit
item5_name = 跌停價
item5_code = {symbol}-DownLimit
item6_name = 未平倉量
item6_code = {symbol}-OI
item7_name = 交易日期
item7_code = {symbol}-TradingDate
item8_name = 剩餘日
item8_code = {symbol}-WRemainDate
item9_name = 昨收價
item9_code = {symbol}-PreClose
item10_name = 昨量
item10_code = {symbol}-PreTotalVolume

# ===== 資料類型設定 =====
[DataType_tick]
items_template = Template_Tick_Items
table_enable_time_newline = true
table_time_newline_interval = 0.800
table_enable_value_change_check = true
table_value_change_check_mode = single
table_value_change_check_items = 總量
log_level = INFO
notification_levels = WARNING,ERROR,CRITICAL

[DataType_order]
items_template = Template_Order_Items
table_enable_time_newline = true
table_time_newline_interval = 0.800
table_enable_value_change_check = true
#table_value_change_check_mode = all
#table_value_change_check_items = all
table_value_change_check_mode = multiple
table_value_change_check_items = 外盤量,累賣成筆
log_level = WARNING
notification_levels = INFO,WARNING,ERROR

[DataType_level2]
items_template = Template_Level2_Items
table_enable_time_newline = true
table_time_newline_interval = 0.800
table_enable_value_change_check = true
table_value_change_check_mode = all
table_value_change_check_items = all
log_level = WARNING
notification_levels = INFO,WARNING,ERROR

[DataType_daily]
items_template = Template_Daily_Items
table_enable_time_newline = true
table_time_newline_interval = 0.800
table_enable_value_change_check = true
table_value_change_check_mode = all
table_value_change_check_items = all
log_level = WARNING
notification_levels = INFO,WARNING,ERROR

# ===== 共用設定 =====
[Common_FileOutput]
enable_data_file = false
enable_complete_data_file = true
enable_log_file = true

[Common_AutoShutdown]
enable_auto_shutdown = False
shutdown_time = 05:55:05
shutdown_buffer_seconds = 30
shutdown_warning_seconds = 10
force_shutdown = True
save_data_before_shutdown = True
disconnect_before_shutdown = True
cleanup_temp_files = True

[Common_Notifications]
enable_system_notifications = True
enable_sound_notifications = False
notify_auto_connect = True
notify_auto_disconnect = True
notify_auto_shutdown = True

# ===== 自動連線模板 =====
[AutoConnect_FuturesDay]
# 期貨日盤 (08:45-13:45)
enable_auto_connect = False
auto_connect_mode = schedule
auto_connect_delay = 5.0
schedule_connect_times = 08:25:00-13:45:15
prevent_weekend_startup = True
schedule_end_action = unadvise_only

[AutoConnect_Stock]
# 股票 (09:00-13:30)
enable_auto_connect = False
auto_connect_mode = schedule
auto_connect_delay = 5.0
schedule_connect_times = 08:25:00-13:35:15
prevent_weekend_startup = True
schedule_end_action = unadvise_only

[AutoConnect_FuturesFullDay]
# 期貨全日 (15:00-05:00, 08:45-13:45)
enable_auto_connect = False
auto_connect_mode = schedule
auto_connect_delay = 5.0
schedule_connect_times = 14:50:00-05:05:05;08:25:00-13:45:15
prevent_weekend_startup = True
schedule_end_action = unadvise_only

[AutoConnect_FuturesNight]
# 期貨夜盤 (15:00-05:00)
enable_auto_connect = False
auto_connect_mode = schedule
auto_connect_delay = 5.0
schedule_connect_times = 14:50:00-05:05:05
prevent_weekend_startup = True
schedule_end_action = unadvise_only

# ===== 商品配置 =====
[FITXN07.TF]
# 台指期貨當月 - 全日交易
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/XQ/FITXN07/_m/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[FIMTXN07.TF]
# 小台指期貨07月 - 全日交易
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/XQ/FIMTXN07/_m/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[FITMN07.TF]
# 微台指期貨07月 - 全日交易
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/XQ/FITMN07/_m/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[FITXN08.TF]
# 台指期貨次月 - 全日交易
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/XQ/FITXN08/_m/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[FIMTXN08.TF]
# 小台指期貨08月 - 全日交易
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/XQ/FIMTXN08/_m/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[FITMN08.TF]
# 微台指期貨08月 - 全日交易
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/XQ/FITMN08/_m/manual/
auto_connect_template = AutoConnect_FuturesFullDay

# -----------------

[FITX07.TF]
# 台指期貨當月 - 日盤交易
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/XQ/FITX07/_m/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[FIMTX07.TF]
# 小台指期貨07月 - 日盤交易
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/XQ/FIMTX07/_m/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[FITM07.TF]
# 微台指期貨07月 - 日盤交易
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/XQ/FITM07/_m/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[FITX08.TF]
# 台指期貨次月 - 日盤交易
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/XQ/FITX08/_m/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[FIMTX08.TF]
# 小台指期貨08月 - 日盤交易
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/XQ/FIMTX08/_m/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[FITM08.TF]
# 微台指期貨08月 - 日盤交易
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/XQ/FITM08/_m/manual/
auto_connect_template = AutoConnect_FuturesFullDay



# 股票範例
[2330.TW]
# 台積電股票
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/2330/_s/manual/
auto_connect_template = AutoConnect_Stock

[2317.TW]
# 鴻海股票
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/2317/_s/manual/
auto_connect_template = AutoConnect_Stock







[TXoN07C22000.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07C22000/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07C22100.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07C22100/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07C22200.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07C22200/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07C22300.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07C22300/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07C22400.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07C22400/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07C22500.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07C22500/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07C22600.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07C22600/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07C22700.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07C22700/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07C22800.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07C22800/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07C22900.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07C22900/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07C23000.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07C23000/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07C23100.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07C23100/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07C23200.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07C23200/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07C23300.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07C23300/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07C23400.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07C23400/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07C23500.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07C23500/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07C23600.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07C23600/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07C23700.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07C23700/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07C23800.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07C23800/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07C23900.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07C23900/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07C24000.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07C24000/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07C24100.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07C24100/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07C24200.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07C24200/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07C24300.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07C24300/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07C24400.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07C24400/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07C24500.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07C24500/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07C24600.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07C24600/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07C24700.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07C24700/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07C24800.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07C24800/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07C24900.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07C24900/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07C25000.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07C25000/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07C25100.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07C25100/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07C25200.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07C25200/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07C25300.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07C25300/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07C25400.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07C25400/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07C25500.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07C25500/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07C25600.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07C25600/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07C25700.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07C25700/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07C25800.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07C25800/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07C25900.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07C25900/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay






[TXoN07P22000.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07P22000/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07P22100.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07P22100/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07P22200.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07P22200/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07P22300.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07P22300/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07P22400.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07P22400/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07P22500.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07P22500/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07P22600.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07P22600/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07P22700.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07P22700/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07P22800.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07P22800/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07P22900.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07P22900/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07P23000.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07P23000/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07P23100.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07P23100/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07P23200.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07P23200/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07P23300.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07P23300/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07P23400.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07P23400/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07P23500.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07P23500/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07P23600.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07P23600/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07P23700.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07P23700/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07P23800.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07P23800/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07P23900.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07P23900/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07P24000.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07P24000/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07P24100.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07P24100/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07P24200.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07P24200/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07P24300.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07P24300/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07P24400.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07P24400/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07P24500.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07P24500/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07P24600.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07P24600/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07P24700.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07P24700/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07P24800.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07P24800/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07P24900.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07P24900/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07P25000.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07P25000/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07P25100.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07P25100/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07P25200.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07P25200/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07P25300.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07P25300/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07P25400.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07P25400/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07P25500.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07P25500/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07P25600.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07P25600/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07P25700.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07P25700/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07P25800.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07P25800/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay

[TXoN07P25900.TF]
# 選擇權07w1C
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/TW/TXoN07P25900/_o/manual/
auto_connect_template = AutoConnect_FuturesFullDay
