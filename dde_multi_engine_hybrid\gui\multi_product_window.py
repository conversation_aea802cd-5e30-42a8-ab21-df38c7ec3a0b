#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多商品DDE监控主窗口
支持多个商品的同时监控和管理
"""

import os
import time
import logging
from datetime import datetime
from collections import deque
from typing import Dict, Optional, List

from PySide6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                             QLabel, QPushButton, QLineEdit, QTableWidget,
                             QTableWidgetItem, QTextEdit, QSplitter, QGroupBox,
                             QFormLayout, QCheckBox, QMessageBox, QApplication,
                             QDialog, QSpinBox, QTabWidget, QTreeWidget,
                             QTreeWidgetItem, QComboBox, QStatusBar)
from PySide6.QtCore import Qt, QTimer, QThread, Signal
from PySide6.QtGui import QFont, QTextCursor

# 导入自定义模块
from dydde.dydde import DDEClient
from core.data_handler import MultiProductDataProcessor
from core.multi_auto_manager import MultiProductAutoManager
from gui.settings_dialog import AutoSettingsDialog
from utils.config_manager import MultiProductConfigManager
from utils.resource_monitor import ResourceMonitor


class NonModalConfirmDialog(QDialog):
    """非模态确认对话框"""

    def __init__(self, parent, title, message, on_confirm_callback):
        super().__init__(parent)
        self.on_confirm_callback = on_confirm_callback

        self.setWindowTitle(title)
        self.setModal(False)  # 设为非模态
        self.setFixedSize(300, 120)

        # 布局
        layout = QVBoxLayout(self)

        # 消息标签
        message_label = QLabel(message)
        message_label.setWordWrap(True)
        layout.addWidget(message_label)

        # 按钮区域
        button_layout = QHBoxLayout()

        confirm_btn = QPushButton("确认")
        confirm_btn.clicked.connect(self.on_confirm)

        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)

        button_layout.addWidget(confirm_btn)
        button_layout.addWidget(cancel_btn)
        layout.addLayout(button_layout)

        # 设定样式
        confirm_btn.setStyleSheet("background-color: #d32f2f; color: white; font-weight: bold;")
        cancel_btn.setStyleSheet("background-color: #757575; color: white;")

    def on_confirm(self):
        """确认按钮点击处理"""
        self.accept()
        if self.on_confirm_callback:
            self.on_confirm_callback()


class MultiProductMainWindow(QMainWindow):
    """多商品DDE监控主窗口"""
    
    # 信号定义
    settings_changed = Signal(dict)
    
    def __init__(self, multi_config: MultiProductConfigManager, logger: logging.Logger):
        super().__init__()
        
        # 基本属性
        self.multi_config = multi_config
        self.logger = logger
        
        # DDE 相关
        self.dde_client = None
        
        # 多商品数据处理
        self.multi_data_processor = MultiProductDataProcessor(multi_config)
        self.process_thread = QThread()

        # 多商品自動化管理器
        self.multi_auto_manager = MultiProductAutoManager(multi_config, logger)

        # 计时器和狀態
        self.last_advise_time = None
        self.timer = QTimer()
        self._auto_shutdown_in_progress = False
        self._manual_exit_confirmed = False

        # GUI更新控制
        self.gui_update_timer = QTimer()
        self.gui_update_interval = 10  # 默认10ms更新一次
        self.pending_data_updates = []

        # GUI 元件
        self.symbol_tabs = None
        self.symbol_widgets = {}  # symbol -> widget_dict
        self.auto_status_label = None
        
        # 资源监控
        self.resource_monitor = ResourceMonitor(self.logger)
        
        # 初始化
        self.init_ui()
        self.init_status_bar()
        self.init_data_processor()
        self.init_auto_manager()
        self.init_timer()
        self.init_gui_update_timer()

        self.logger.info("多商品主窗口初始化完成")

    def set_engine_components(self, engine_manager, data_bridge):
        """设置引擎组件

        Args:
            engine_manager: 引擎管理器
            data_bridge: 数据桥接器
        """
        try:
            self.engine_manager = engine_manager
            self.data_bridge = data_bridge

            # 设置数据桥接器的GUI更新回调
            if self.data_bridge:
                self.data_bridge.on_global_update = self._on_engine_data_update

            self.logger.info("引擎组件设置完成")

        except Exception as e:
            self.logger.error(f"设置引擎组件失败: {str(e)}")

    def _on_engine_data_update(self, product_symbol: str, data_type: str, item: str, value: str):
        """引擎数据更新回调"""
        try:
            # 更新GUI显示
            self._update_symbol_data_display(product_symbol, data_type, item, value)

        except Exception as e:
            self.logger.error(f"引擎数据更新回调失败: {product_symbol}.{data_type}, {item}, {str(e)}")

    def _update_symbol_data_display(self, product_symbol: str, data_type: str, item: str, value: str):
        """更新商品数据显示"""
        try:
            # 这里可以添加具体的GUI更新逻辑
            # 暂时只记录日志
            self.logger.debug(f"GUI数据更新: {product_symbol}.{data_type} {item}={value}")

        except Exception as e:
            self.logger.error(f"更新商品数据显示失败: {product_symbol}.{data_type}, {item}, {str(e)}")

    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("多商品DDE监控程式 v7.1_multi_engine_hybrid")
        self.setGeometry(100, 100, 1400, 900)
        
        # 中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建各个区域
        self.create_connection_area(main_layout)
        self.create_auto_status_area(main_layout)  # 恢复自動化功能区域

        # 创建可调整的分隔器，分隔商品监控和日誌区域
        splitter = QSplitter(Qt.Vertical)

        # 创建商品监控区域
        symbol_widget = QWidget()
        symbol_layout = QVBoxLayout(symbol_widget)
        self.create_symbol_tabs_area(symbol_layout)
        splitter.addWidget(symbol_widget)

        # 创建日誌区域
        log_widget = QWidget()
        log_layout = QVBoxLayout(log_widget)
        self.create_log_area(log_layout)
        splitter.addWidget(log_widget)

        # 设置分隔器比例 (商品监控:日誌 = 3:1)
        splitter.setSizes([600, 200])

        main_layout.addWidget(splitter)
        
        # 设定字体
        font = QFont("Microsoft YaHei", 9)
        self.setFont(font)

    def init_status_bar(self):
        """初始化狀態栏"""
        try:
            # 创建狀態栏
            self.status_bar = QStatusBar()
            self.setStatusBar(self.status_bar)

            # 狀態栏左侧信息
            self.status_bar.showMessage("多商品DDE監控程式已啟動")

            # 狀態栏右侧时间顯示
            self.datetime_label = QLabel()
            self.datetime_label.setStyleSheet("color: blue; font-weight: bold;")
            self.status_bar.addPermanentWidget(self.datetime_label)

            # 啟動时间更新定时器
            self.datetime_timer = QTimer()
            self.datetime_timer.timeout.connect(self.update_datetime_display)
            self.datetime_timer.start(100)  # 每100ms更新一次，顯示毫秒

            self.logger.info("狀態欄初始化完成")

        except Exception as e:
            self.logger.error(f"初始化狀態欄失敗: {str(e)}")

    def update_datetime_display(self):
        """更新日期时间顯示"""
        try:
            current_time = datetime.now()
            time_str = current_time.strftime('%Y/%m/%d %H:%M:%S.%f')[:-3]  # 顯示毫秒
            self.datetime_label.setText(time_str)
        except Exception as e:
            self.logger.error(f"更新日期時間顯示失敗: {str(e)}")
    
    def create_connection_area(self, parent_layout):
        """创建連接控制区域"""
        group = QGroupBox("DDE 連接設定")
        layout = QFormLayout(group)
        
        # 服务和主题输入
        self.service_edit = QLineEdit()
        self.topic_edit = QLineEdit()
        
        # 从配置文件载入预设值
        system_config = self.multi_config.get_system_config()
        self.service_edit.setText(system_config.get('dde_service', 'XQTISC'))
        self.topic_edit.setText(system_config.get('dde_topic', 'Quote'))
        
        layout.addRow("服務名稱:", self.service_edit)
        layout.addRow("主題:", self.topic_edit)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        self.service_connect_btn = QPushButton("服務連線")
        self.service_connect_btn.clicked.connect(self.toggle_service_connection)

        self.test_items_btn = QPushButton("測試項目")
        self.test_items_btn.clicked.connect(self.test_items)
        self.test_items_btn.setEnabled(False)

        self.subscribe_btn = QPushButton("訂閱項目")
        self.subscribe_btn.clicked.connect(self.toggle_subscription)
        self.subscribe_btn.setEnabled(False)
        
        self.connect_btn = QPushButton("連線並訂閱")
        self.connect_btn.clicked.connect(self.toggle_connection)
        
        button_layout.addWidget(self.service_connect_btn)
        button_layout.addWidget(self.test_items_btn)
        button_layout.addWidget(self.subscribe_btn)
        button_layout.addWidget(self.connect_btn)
        button_layout.addStretch()

        # 狀態标签
        self.status_label = QLabel("尚未連線")
        self.status_label.setStyleSheet("color: red; font-weight: bold;")
        button_layout.addWidget(self.status_label)

        layout.addRow(button_layout)
        
        layout.addRow(button_layout)
        parent_layout.addWidget(group)

    def create_auto_status_area(self, parent_layout):
        """创建自動化狀態区域"""
        group = QGroupBox("自動化功能")
        layout = QHBoxLayout(group)

        # 自動狀態顯示
        self.auto_status_label = QLabel("⚫ 手動連線 | ⚫ 手動結束")
        self.auto_status_label.setStyleSheet("color: blue; font-weight: bold;")
        layout.addWidget(self.auto_status_label)

        layout.addStretch()

        # GUI更新频率控制
        gui_control_layout = QHBoxLayout()
        gui_control_layout.addWidget(QLabel("GUI更新間隔:"))

        self.gui_update_spinbox = QSpinBox()
        self.gui_update_spinbox.setRange(5, 5000)  # 5ms 到 5秒
        self.gui_update_spinbox.setValue(self.gui_update_interval)
        self.gui_update_spinbox.setSuffix(" ms")
        self.gui_update_spinbox.valueChanged.connect(self.on_gui_update_interval_changed)
        gui_control_layout.addWidget(self.gui_update_spinbox)

        layout.addLayout(gui_control_layout)
        layout.addStretch()

        # 自動連線開關
        self.auto_connect_checkbox = QCheckBox("自動連線")
        self.auto_connect_checkbox.setChecked(True)  # 默認開啟
        self.auto_connect_checkbox.stateChanged.connect(self.on_auto_connect_changed)
        layout.addWidget(self.auto_connect_checkbox)

        # 自動結束開關
        self.auto_end_checkbox = QCheckBox("自動結束")
        self.auto_end_checkbox.setChecked(True)  # 默認開啟
        self.auto_end_checkbox.stateChanged.connect(self.on_auto_end_changed)
        layout.addWidget(self.auto_end_checkbox)

        # 資源監控控制
        resource_layout = QHBoxLayout()
        self.resource_monitor_btn = QPushButton("開始監控")
        self.resource_monitor_btn.setCheckable(True)
        self.resource_monitor_btn.clicked.connect(self.toggle_resource_monitoring)
        self.resource_stats_btn = QPushButton("資源統計")
        self.resource_stats_btn.clicked.connect(self.show_resource_stats)
        resource_layout.addWidget(self.resource_monitor_btn)
        resource_layout.addWidget(self.resource_stats_btn)
        layout.addLayout(resource_layout)

        # 设定按钮
        self.auto_settings_btn = QPushButton("自動化設定")
        self.auto_settings_btn.setStyleSheet("background-color: green; color: white; font-weight: bold;")
        self.auto_settings_btn.clicked.connect(self.show_auto_settings)
        layout.addWidget(self.auto_settings_btn)

        parent_layout.addWidget(group)



    def create_symbol_tabs_area(self, parent_layout):
        """创建商品标签页区域"""
        group = QGroupBox("商品監控項目")
        layout = QVBoxLayout(group)
        
        # 创建标签页控件
        self.symbol_tabs = QTabWidget()
        
        # 为每个商品创建标签页
        for symbol in self.multi_config.symbols:
            self.create_symbol_tab(symbol)
        
        layout.addWidget(self.symbol_tabs)
        parent_layout.addWidget(group)
    
    def create_symbol_tab(self, symbol: str):
        """为指定商品创建标签页"""
        try:
            # 创建标签页
            tab_widget = QWidget()
            tab_layout = QVBoxLayout(tab_widget)
            
            # 商品信息区域
            info_layout = QHBoxLayout()
            info_layout.addWidget(QLabel(f"商品: {symbol}"))
            
            # 获取商品配置
            symbol_config = self.multi_config.symbol_configs.get(symbol, {})
            enabled_types = symbol_config.get('enabled_types', '')
            info_layout.addWidget(QLabel(f"数据类型: {enabled_types}"))
            
            output_path = self.multi_config.get_symbol_output_path(symbol)
            info_layout.addWidget(QLabel(f"输出路径: {output_path}"))
            
            info_layout.addStretch()
            tab_layout.addLayout(info_layout)
            
            # 数据类型标签页
            datatype_tabs = QTabWidget()
            
            # 为每个数据类型创建子标签页
            symbol_widgets = {}
            for data_type in enabled_types.split(','):
                data_type = data_type.strip()
                if data_type:
                    datatype_widget = self.create_datatype_widget(symbol, data_type)
                    datatype_tabs.addTab(datatype_widget, data_type.upper())
                    symbol_widgets[data_type] = datatype_widget
            
            tab_layout.addWidget(datatype_tabs)
            
            # 添加到主标签页
            self.symbol_tabs.addTab(tab_widget, symbol)
            self.symbol_widgets[symbol] = symbol_widgets
            
        except Exception as e:
            self.logger.error(f"创建商品标签页失敗 {symbol}: {str(e)}")
    
    def create_datatype_widget(self, symbol: str, data_type: str):
        """创建数据类型顯示控件"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 項目表格
        items_table = QTableWidget()
        items_table.setColumnCount(5)
        items_table.setHorizontalHeaderLabels(["項目名稱", "項目代碼", "當前值", "狀態", "更新時間"])
        
        # 设定表格属性
        items_table.setAlternatingRowColors(True)
        items_table.setSelectionBehavior(QTableWidget.SelectRows)
        
        # 获取項目数据並填充表格
        items = self.multi_config.get_symbol_items(symbol, data_type)
        items_table.setRowCount(len(items))
        
        for row, (item_code, item_name) in enumerate(items.items()):
            items_table.setItem(row, 0, QTableWidgetItem(item_name))
            items_table.setItem(row, 1, QTableWidgetItem(item_code))
            items_table.setItem(row, 2, QTableWidgetItem(""))
            items_table.setItem(row, 3, QTableWidgetItem(""))
        
        layout.addWidget(items_table)
        
        # 保存表格引用以便后续更新
        widget.items_table = items_table
        widget.symbol = symbol
        widget.data_type = data_type

        return widget

    def create_log_area(self, parent_layout):
        """创建日誌顯示区域"""
        group = QGroupBox("程式日誌")
        layout = QVBoxLayout(group)

        # 日誌控制按钮
        log_control_layout = QHBoxLayout()
        self.log_pause_btn = QPushButton("暫停")
        self.log_pause_btn.setCheckable(True)
        self.log_pause_btn.clicked.connect(self.toggle_log_pause)
        self.log_clear_btn = QPushButton("清除")
        self.log_clear_btn.clicked.connect(self.clear_log)
        log_control_layout.addWidget(self.log_pause_btn)
        log_control_layout.addWidget(self.log_clear_btn)
        log_control_layout.addStretch()
        layout.addLayout(log_control_layout)

        # 日誌文本区域
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        self.log_text.setFont(QFont("Consolas", 9))
        self.log_text.setReadOnly(True)
        layout.addWidget(self.log_text)

        # 初始化日誌顯示
        self.log_paused = False
        self.setup_log_handler()

        parent_layout.addWidget(group)

    def init_data_processor(self):
        """初始化数据处理器"""
        try:
            # 移动数据处理器到独立线程
            self.multi_data_processor.moveToThread(self.process_thread)

            # 連接信号
            self.multi_data_processor.data_processed.connect(self._on_data_processed)
            self.process_thread.started.connect(self.multi_data_processor.process_data)

            # 啟動线程
            self.process_thread.start()

            self.logger.info("多商品数据处理器初始化完成")

        except Exception as e:
            self.logger.error(f"初始化多商品数据处理器失敗: {str(e)}")

    def init_auto_manager(self):
        """初始化自動化管理器"""
        try:
            # 連接自動化管理器信号
            self.multi_auto_manager.symbol_auto_connect_requested.connect(self.auto_connect_symbol)
            self.multi_auto_manager.symbol_auto_disconnect_requested.connect(self.auto_disconnect_symbol)
            self.multi_auto_manager.symbol_auto_unadvise_only_requested.connect(self.auto_unadvise_symbol)
            self.multi_auto_manager.auto_shutdown_requested.connect(self.auto_shutdown)
            self.multi_auto_manager.status_changed.connect(self.update_auto_status_display)

            # 延迟啟動自動化管理器，等GUI完全绘制完成
            QTimer.singleShot(2000, self.start_auto_manager)  # 2秒后啟動

            self.logger.info("多商品自動化管理器初始化完成")

        except Exception as e:
            self.logger.error(f"初始化多商品自動化管理器失敗: {str(e)}")

    def start_auto_manager(self):
        """啟動自動化管理器"""
        try:
            self.multi_auto_manager.start()
            self.logger.info("多商品自動化管理器已啟動")
        except Exception as e:
            self.logger.error(f"啟動多商品自動化管理器失敗: {str(e)}")

    def on_auto_connect_changed(self, state):
        """自動連線開關狀態改變"""
        try:
            enabled = state == 2  # Qt.Checked
            self.logger.info(f"自動連線開關: {'開啟' if enabled else '關閉'}")
            self.update_auto_status_display()
        except Exception as e:
            self.logger.error(f"處理自動連線開關變更失敗: {str(e)}")

    def on_auto_end_changed(self, state):
        """自動結束開關狀態改變"""
        try:
            enabled = state == 2  # Qt.Checked
            self.logger.info(f"自動結束開關: {'開啟' if enabled else '關閉'}")
            self.update_auto_status_display()
        except Exception as e:
            self.logger.error(f"處理自動結束開關變更失敗: {str(e)}")

    def update_auto_status_display(self):
        """更新自動化狀態顯示"""
        try:
            auto_connect = "🟢 自動連線" if self.auto_connect_checkbox.isChecked() else "⚫ 手動連線"
            auto_end = "🟢 自動結束" if self.auto_end_checkbox.isChecked() else "⚫ 手動結束"
            self.auto_status_label.setText(f"{auto_connect} | {auto_end}")
        except Exception as e:
            self.logger.error(f"更新自動化狀態顯示失敗: {str(e)}")

    def init_timer(self):
        """初始化定时器"""
        try:
            self.timer.timeout.connect(self.update_display)
            self.timer.start(1000)  # 每秒更新一次顯示

        except Exception as e:
            self.logger.error(f"初始化定时器失敗: {str(e)}")

    def init_gui_update_timer(self):
        """初始化GUI更新定时器"""
        try:
            self.gui_update_timer.timeout.connect(self.process_pending_updates)
            self.gui_update_timer.start(self.gui_update_interval)

        except Exception as e:
            self.logger.error(f"初始化GUI更新定时器失敗: {str(e)}")

    def toggle_service_connection(self):
        """切換服务連接狀態"""
        try:
            if self.dde_client is None:
                # 获取服务和主题
                service = self.service_edit.text().strip()
                topic = self.topic_edit.text().strip()

                if not service or not topic:
                    self.status_label.setText("請輸入服務名稱和主題")
                    self.status_label.setStyleSheet("color: red; font-weight: bold;")
                    return

                # 创建DDE客户端
																					 
                self.dde_client = DDEClient(service, topic)

                # 連接服务
                try:
                    self.dde_client.connect()
                    # 检查連接狀態
                    if self.dde_client.is_connected():
                        self.status_label.setText("服務已連接")
                        self.status_label.setStyleSheet("color: green; font-weight: bold;")
                        self.service_connect_btn.setText("斷離服務")
                        self.test_items_btn.setEnabled(True)
                        self.subscribe_btn.setEnabled(True)
                        self.logger.info(f"DDE服務連接成功: {service}/{topic}")
                    else:
                        self.status_label.setText("服務連接失敗")
                        self.status_label.setStyleSheet("color: red; font-weight: bold;")
                        self.logger.error("DDE服務連接失敗")
                        self.dde_client = None
                except Exception as e:
                    self.status_label.setText("服務連接失敗")
                    self.status_label.setStyleSheet("color: red; font-weight: bold;")
                    self.logger.error(f"DDE服務連接異常: {str(e)}")
                    self.dde_client = None
            else:
                # 断开連接
                self.disconnect_service()

        except Exception as e:
            self.logger.error(f"切換服务連接失敗: {str(e)}")
            self.status_label.setText("連接錯誤")
            self.status_label.setStyleSheet("color: red; font-weight: bold;")

    def disconnect_service(self):
        """断开DDE服务"""
        try:
            if self.dde_client:
                self.dde_client.disconnect()
                self.dde_client = None

            self.status_label.setText("服務已斷開")
            self.status_label.setStyleSheet("color: red; font-weight: bold;")
            self.service_connect_btn.setText("服務連線")
            self.connect_btn.setText("連線並訂閱")  # 添加连线按钮状态更新
            self.test_items_btn.setEnabled(False)
            self.subscribe_btn.setEnabled(False)
            self.subscribe_btn.setText("訂閱項目")

            self.logger.info("DDE服务已断开")

        except Exception as e:
            self.logger.error(f"断开DDE服务失敗: {str(e)}")

    def test_items(self):
        """测试所有商品的监控項目"""
        try:
            if not self.dde_client:
                self.logger.warning("DDE客户端未連接")
                return

            all_items = self.multi_config.get_all_symbol_items()
            test_count = 0
            success_count = 0

            for symbol, symbol_data in all_items.items():
                for data_type, items in symbol_data.items():
                    for item_code, item_name in items.items():
                        test_count += 1
                        try:
                            value = self.dde_client.request(item_code)
                            if value is not None:
                                success_count += 1
                                self.logger.info(f"測試成功: {item_name}({item_code}) = {value}")
                                # 更新表格顯示
                                self.update_item_in_table(symbol, data_type, item_code, value, "測試成功")
                            else:
                                self.logger.warning(f"測試失敗: {item_name}({item_code})")
                                self.update_item_in_table(symbol, data_type, item_code, "", "測試失敗")
                        except Exception as e:
                            self.logger.error(f"測試項目失敗 {item_code}: {str(e)}")
                            self.update_item_in_table(symbol, data_type, item_code, "", "測試錯誤")

            self.logger.info(f"項目测试完成: {success_count}/{test_count} 成功")

            if success_count > 0:
                self.subscribe_btn.setEnabled(True)

        except Exception as e:
            self.logger.error(f"测试項目失敗: {str(e)}")

    def toggle_subscription(self):
        """切換訂閱狀態"""
        try:
            if not self.dde_client:
                self.logger.warning("DDE客户端未連接")
                return

            if self.subscribe_btn.text() == "訂閱項目":
                # 开始訂閱
                self.subscribe_all_items()
            else:
                # 取消訂閱
                self.unsubscribe_all_items()

        except Exception as e:
            self.logger.error(f"切換訂閱狀態失敗: {str(e)}")

    def subscribe_all_items(self):
        """測試並訂閱所有商品的监控項目 - 實現與模組化版本相同的邏輯"""
        try:
            all_items = self.multi_config.get_all_symbol_items()

            # 第一步：測試所有項目並獲取初始值
            self.logger.info("開始測試所有項目...")
            test_count = 0
            test_success_count = 0

            for symbol, symbol_data in all_items.items():
                for data_type, items in symbol_data.items():
                    for item_code, item_name in items.items():
                        test_count += 1
                        try:
                            # 測試項目 - 獲取初始值
                            initial_value = self.dde_client.request(item_code)
                            if initial_value is not None:
                                test_success_count += 1
                                # 將初始值存儲到數據處理器中
                                self.multi_data_processor.set_initial_value(symbol, data_type, item_code, item_name, initial_value)
                                self.logger.debug(f"測試成功: {item_name}({item_code}) = {initial_value}")
                                self.update_item_in_table(symbol, data_type, item_code, initial_value, "測試成功")
                            else:
                                self.logger.warning(f"測試失敗: {item_name}({item_code})")
                                self.update_item_in_table(symbol, data_type, item_code, "", "測試失敗")
                        except Exception as e:
                            self.logger.error(f"測試項目失敗 {item_code}: {str(e)}")
                            self.update_item_in_table(symbol, data_type, item_code, "", "測試錯誤")

            self.logger.info(f"項目測試完成: {test_success_count}/{test_count} 成功")

            # 第二步：訂閱所有測試成功的項目
            self.logger.info("開始訂閱所有項目...")
            subscribe_count = 0
            success_count = 0

            for symbol, symbol_data in all_items.items():
                for data_type, items in symbol_data.items():
                    for item_code, item_name in items.items():
                        # 只訂閱測試成功的項目
                        if self.multi_data_processor.has_initial_value(symbol, data_type, item_code):
                            subscribe_count += 1
                            try:
                                # 为每个項目提供回调函数
                                if self.dde_client.advise(item_code, self.on_advise_data):
                                    success_count += 1
                                    self.logger.info(f"訂閱成功: {item_name}({item_code})")
                                    self.update_item_in_table(symbol, data_type, item_code, "", "已訂閱")
                                else:
                                    self.logger.warning(f"訂閱失敗: {item_name}({item_code})")
                                    self.update_item_in_table(symbol, data_type, item_code, "", "訂閱失敗")
                            except Exception as e:
                                self.logger.error(f"訂閱項目失敗 {item_code}: {str(e)}")
                                self.update_item_in_table(symbol, data_type, item_code, "", "訂閱錯誤")

            self.logger.info(f"項目訂閱完成: {success_count}/{subscribe_count} 成功")

            if success_count > 0:
                self.subscribe_btn.setText("取消訂閱")
                self.connect_btn.setText("退訂並斷線")  # 添加连线按钮状态更新
                self.status_label.setText(f"測試: {test_success_count}/{test_count}, 訂閱: {success_count}/{subscribe_count}")
                self.status_label.setStyleSheet("color: blue; font-weight: bold;")

        except Exception as e:
            self.logger.error(f"測試並訂閱所有項目失敗: {str(e)}")

    def unsubscribe_all_items(self):
        """取消訂閱所有項目"""
        try:
            all_items = self.multi_config.get_all_symbol_items()

            for symbol, symbol_data in all_items.items():
                for data_type, items in symbol_data.items():
                    for item_code, item_name in items.items():
                        try:
                            self.dde_client.unadvise(item_code)
                            self.update_item_in_table(symbol, data_type, item_code, "", "已取消")
                        except Exception as e:
                            self.logger.error(f"取消訂閱失敗 {item_code}: {str(e)}")

            self.subscribe_btn.setText("訂閱項目")
            self.connect_btn.setText("連線並訂閱")  # 添加连线按钮状态更新
            self.status_label.setText("已取消所有訂閱")
            self.status_label.setStyleSheet("color: orange; font-weight: bold;")
            self.logger.info("已取消所有項目訂閱")

        except Exception as e:
            self.logger.error(f"取消訂閱所有項目失敗: {str(e)}")

    def toggle_connection(self):
        """切換連接並訂閱狀態"""
        try:
            if self.dde_client is None or self.subscribe_btn.text() == "訂閱項目":
                # 連接並訂閱
                if self.dde_client is None:
                    self.toggle_service_connection()

                if self.dde_client and self.test_items_btn.isEnabled():
                    self.subscribe_all_items()
                    self.connect_btn.setText("退訂並斷線")
            else:
                # 断开並取消訂閱
                self.unsubscribe_all_items()
                self.disconnect_service()
                self.connect_btn.setText("連線並訂閱")

        except Exception as e:
            self.logger.error(f"切換連接並訂閱狀態失敗: {str(e)}")

    def update_item_in_table(self, symbol: str, data_type: str, item_code: str,
                           value: str, status: str = ""):
        """更新表格中的項目顯示"""
        try:
            if symbol not in self.symbol_widgets:
                return

            if data_type not in self.symbol_widgets[symbol]:
                return

            widget = self.symbol_widgets[symbol][data_type]
            table = widget.items_table

            # 查找对应的行
            for row in range(table.rowCount()):
                if table.item(row, 1) and table.item(row, 1).text() == item_code:
                    # 更新值
                    if value:
                        table.setItem(row, 2, QTableWidgetItem(value))

                    # 更新狀態
                    if status:
                        status_item = QTableWidgetItem(status)
                        # 根据狀態设置颜色
                        if "成功" in status or "已訂閱" in status or "自動訂閱" in status:
                            status_item.setBackground(Qt.green)
                        elif "失敗" in status or "錯誤" in status:
                            status_item.setBackground(Qt.red)
                        elif "已取消" in status or "自動取消" in status or "自動結束" in status:
                            status_item.setBackground(Qt.yellow)
                        table.setItem(row, 3, status_item)

                    # 更新时间
                    current_time = datetime.now().strftime('%H:%M:%S')
                    table.setItem(row, 4, QTableWidgetItem(current_time))

                    # 更新狀態颜色
                    if status == "測試成功" or status == "已訂閱":
                        table.item(row, 1).setBackground(Qt.green)
                    elif status == "測試失敗" or status == "訂閱失敗":
                        table.item(row, 1).setBackground(Qt.red)
                    elif status == "已取消":
                        table.item(row, 1).setBackground(Qt.yellow)

                    break

        except Exception as e:
            self.logger.error(f"更新表格項目失敗: {str(e)}")

    def on_advise_data(self, item: str, value: str):
        """处理DDE数据更新回调

        Args:
            item: DDE項目名称
            value: 項目值
        """
        try:
            # 记录最后接收数据的时间
            self.last_advise_time = time.time()

            # 将数据加入多商品处理器
            self.multi_data_processor.add_data(item, value)

        except Exception as e:
            self.logger.error(f"处理DDE数据回调失敗: {str(e)}")

    def _on_data_processed(self, symbol: str, data_type: str, item: str, value: str):
        """处理数据处理完成信号"""
        try:
            # 添加到待更新队列
            self.pending_data_updates.append((symbol, data_type, item, value))

        except Exception as e:
            self.logger.error(f"处理数据处理信号失敗: {str(e)}")

    def process_pending_updates(self):
        """处理待更新的数据"""
        try:
            if not self.pending_data_updates:
                return

            # 批量处理更新
            updates_to_process = self.pending_data_updates[:50]  # 每次最多处理50个更新
            self.pending_data_updates = self.pending_data_updates[50:]

            for symbol, data_type, item, value in updates_to_process:
                # 数据更新时保持"已訂閱"状态，不改为"已更新"
                self.update_item_in_table(symbol, data_type, item, value, "已訂閱")

        except Exception as e:
            self.logger.error(f"处理待更新数据失敗: {str(e)}")

    def update_display(self):
        """更新顯示"""
        try:
            # 更新狀態信息
            if self.last_advise_time:
                elapsed = time.time() - self.last_advise_time
                if elapsed > 10:  # 超过10秒没有数据
                    self.status_label.setText(f"數據延遲 {elapsed:.1f}s")
                    self.status_label.setStyleSheet("color: orange; font-weight: bold;")

        except Exception as e:
            self.logger.error(f"更新顯示失敗: {str(e)}")

    def on_gui_update_interval_changed(self, value):
        """GUI更新间隔改變处理"""
        try:
            self.gui_update_interval = value
            self.gui_update_timer.stop()
            self.gui_update_timer.start(value)
            self.logger.info(f"GUI更新间隔已改为: {value}ms")

        except Exception as e:
            self.logger.error(f"改變GUI更新间隔失敗: {str(e)}")

    def toggle_resource_monitoring(self):
        """切換资源监控狀態"""
        try:
            if self.resource_monitor_btn.isChecked():
                self.resource_monitor.start_monitoring()
                self.resource_monitor_btn.setText("停止監控")
                self.resource_monitor_btn.setStyleSheet("background-color: lightcoral;")
                self.logger.info("資源監控已啟動")
            else:
                self.resource_monitor.stop_monitoring()
                self.resource_monitor_btn.setText("開始監控")
                self.resource_monitor_btn.setStyleSheet("")
                self.logger.info("資源監控已停止")

        except Exception as e:
            self.logger.error(f"切換資源監控狀態失敗: {str(e)}")

    def show_resource_stats(self):
        """顯示资源统计"""
        try:
            stats = self.resource_monitor.get_current_stats()
            if stats:
                message = f"""資源使用統計 (監控時間: {stats['monitoring_duration']}):

CPU使用率:
  當前: {stats['cpu']['current']:.1f}%
  平均: {stats['cpu']['avg']:.1f}%
  最大: {stats['cpu']['max']:.1f}%

記憶體使用:
  當前: {stats['memory_mb']['current']:.1f}MB
  平均: {stats['memory_mb']['avg']:.1f}MB
  最大: {stats['memory_mb']['max']:.1f}MB

線程數: {stats['threads']['current']:.0f}
數據點數: {stats['data_points']}"""
                QMessageBox.information(self, "資源統計", message)
            else:
                QMessageBox.information(self, "資源統計", "暫無資源統計數據")

        except Exception as e:
            self.logger.error(f"顯示資源統計失敗: {str(e)}")

    def auto_connect_symbol(self, symbol: str):
        """自动連接指定商品 - 修復版本：確保DDE連線穩定性"""
        try:
            self.logger.info(f"自動連接商品: {symbol}")

            # 如果DDE客户端未連接，先連接服务
            if self.dde_client is None or not self.dde_client.is_connected():
                # 如果有舊的客戶端，先清理
                if self.dde_client is not None:
                    try:
                        self.logger.info("清理舊的DDE連線")
                        self.dde_client.disconnect()
                        self.dde_client = None
                    except Exception as cleanup_e:
                        self.logger.warning(f"清理舊DDE連線時發生錯誤: {str(cleanup_e)}")

                # 获取系统配置中的服务和主题
                system_config = self.multi_config.get_system_config()
                service = system_config.get('dde_service', 'XQTISC')
                topic = system_config.get('dde_topic', 'Quote')

                # 创建新的DDE客户端
                self.dde_client = DDEClient(service, topic)

                # 連接服务
                try:
                    self.logger.info(f"正在連接DDE服務: {service}/{topic}")
                    self.dde_client.connect()

                    # 等待連線穩定
                    import time
                    time.sleep(0.5)

                    # 检查連接狀態
                    if not self.dde_client.is_connected():
                        self.logger.error(f"自動連接DDE服務失敗: {service}/{topic}")
                        # 通知自动管理器连接失败
                        if hasattr(self, 'multi_auto_manager') and self.multi_auto_manager:
                            self.multi_auto_manager.notify_connection_completed(symbol, False)
                        return
                    else:
                        # 更新連接狀態顯示
                        self.status_label.setText("服務已連接")
                        self.status_label.setStyleSheet("color: green; font-weight: bold;")
                        self.service_connect_btn.setText("斷離服務")
                        self.test_items_btn.setEnabled(True)
                        self.subscribe_btn.setEnabled(True)
                        self.logger.info(f"自動連接DDE服務成功: {service}/{topic}")

                        # 更新狀態顯示
                        self.status_label.setText("服務已連接，準備訂閱...")
                        self.status_label.setStyleSheet("color: blue; font-weight: bold;")

                        # 延迟开始訂閱所有商品，确保連接稳定
                        QTimer.singleShot(500, self.auto_subscribe_all_symbols)
                        return

                except Exception as e:
                    self.logger.error(f"自動連接DDE服務異常: {service}/{topic} - {str(e)}")
                    # 通知自动管理器连接失败
                    if hasattr(self, 'multi_auto_manager') and self.multi_auto_manager:
                        self.multi_auto_manager.notify_connection_completed(symbol, False)
                    return
            else:
                # 如果已经連接，先清理之前的訂閱，然後開始新的訂閱
                self.logger.info(f"DDE已連接，準備為商品 {symbol} 清理並重新訂閱")

                # 清理之前的訂閱狀態
                try:
                    # 取消所有現有訂閱
                    if hasattr(self, 'items_data') and self.items_data:
                        subscribed_count = 0
                        for item in self.items_data.values():
                            if item.status == "已訂閱":
                                try:
                                    self.dde_client.unadvise(item.code)
                                    item.status = "已測試"
                                    subscribed_count += 1
                                except Exception as unadvise_e:
                                    self.logger.warning(f"取消訂閱 {item.code} 失敗: {str(unadvise_e)}")

                        if subscribed_count > 0:
                            self.logger.info(f"已清理 {subscribed_count} 個舊訂閱項目")
                            # 等待清理完成
                            time.sleep(0.3)

                    # 開始所有商品的訂閱
                    self.auto_subscribe_all_symbols()

                except Exception as cleanup_e:
                    self.logger.error(f"清理舊訂閱失敗: {str(cleanup_e)}")
                    # 即使清理失敗，也嘗試繼續訂閱所有商品
                    self.auto_subscribe_all_symbols()

        except Exception as e:
            self.logger.error(f"自動連接商品失敗 {symbol}: {str(e)}")
            # 通知自动管理器连接失败
            if hasattr(self, 'multi_auto_manager') and self.multi_auto_manager:
                self.multi_auto_manager.notify_connection_completed(symbol, False)

    def auto_subscribe_all_symbols(self):
        """自動測試並訂閱所有商品 - 使用與手動連線相同的處理方式"""
        try:
            self.logger.info("開始自動測試並訂閱所有商品")

            if self.dde_client and self.dde_client.is_connected():
                all_items = self.multi_config.get_all_symbol_items()

                # 第一步：測試所有項目並獲取初始值 - 非阻塞處理
                self.logger.info("開始測試所有項目...")
                test_count = 0
                test_success_count = 0
                batch_size = 20  # 每批處理20個項目後讓GUI響應

                for symbol, symbol_data in all_items.items():
                    for data_type, items in symbol_data.items():
                        for item_code, item_name in items.items():
                            test_count += 1
                            try:
                                # 測試項目 - 獲取初始值
                                initial_value = self.dde_client.request(item_code)
                                if initial_value is not None:
                                    test_success_count += 1
                                    # 將初始值存儲到數據處理器中
                                    self.multi_data_processor.set_initial_value(symbol, data_type, item_code, item_name, initial_value)
                                    self.logger.debug(f"自動測試成功: {item_name}({item_code}) = {initial_value}")
                                    self.update_item_in_table(symbol, data_type, item_code, initial_value, "自動測試成功")
                                else:
                                    self.logger.warning(f"自動測試失敗: {item_name}({item_code})")
                                    self.update_item_in_table(symbol, data_type, item_code, "", "自動測試失敗")

                            except Exception as e:
                                self.logger.error(f"自動測試項目失敗 {item_code}: {str(e)}")
                                self.update_item_in_table(symbol, data_type, item_code, "", "自動測試錯誤")

                            # 每批次處理後讓GUI響應，避免界面凍結
                            if test_count % batch_size == 0:
                                QApplication.processEvents()  # 處理GUI事件
                                #progress = (test_count / (len(all_items) * 46)) * 50  # 測試階段佔50%
                                progress = (test_count / (len(self.multi_config.symbols) * 46)) * 50  # 測試階段佔50%
                                self.status_label.setText(f"測試進度: {test_count} 項目 ({progress:.1f}%)")
                                self.status_label.setStyleSheet("color: orange; font-weight: bold;")

                self.logger.info(f"項目測試完成: {test_success_count}/{test_count} 成功")

                # 第二步：訂閱所有測試成功的項目 - 非阻塞處理
                self.logger.info("開始訂閱所有項目...")
                subscribe_count = 0
                success_count = 0

                for symbol, symbol_data in all_items.items():
                    for data_type, items in symbol_data.items():
                        for item_code, item_name in items.items():
                            # 只訂閱測試成功的項目
                            if self.multi_data_processor.has_initial_value(symbol, data_type, item_code):
                                subscribe_count += 1
                                try:
                                    # 为每个項目提供回调函数
                                    if self.dde_client.advise(item_code, self.on_advise_data):
                                        success_count += 1
                                        self.logger.debug(f"自動訂閱成功: {item_name}({item_code})")
                                        self.update_item_in_table(symbol, data_type, item_code, "", "已訂閱")
                                    else:
                                        self.logger.warning(f"自動訂閱失敗: {item_name}({item_code})")
                                        self.update_item_in_table(symbol, data_type, item_code, "", "訂閱失敗")
                                except Exception as e:
                                    self.logger.error(f"自動訂閱項目失敗 {item_code}: {str(e)}")
                                    self.update_item_in_table(symbol, data_type, item_code, "", "訂閱錯誤")

                                # 每批次處理後讓GUI響應，避免界面凍結
                                if subscribe_count % batch_size == 0:
                                    QApplication.processEvents()  # 處理GUI事件
                                    #progress = 50 + (success_count / subscribe_count) * 50  # 訂閱階段佔50%
                                    #self.status_label.setText(f"訂閱進度: {success_count}/{subscribe_count} 項目 ({progress:.1f}%)")
                                    progress = 50 + (success_count / (len(self.multi_config.symbols) * 46)) * 50  # 訂閱階段佔50%
                                    self.status_label.setText(f"訂閱進度: {success_count}/{len(self.multi_config.symbols) * 46} 項目 ({progress:.1f}%)")
                                    self.status_label.setStyleSheet("color: blue; font-weight: bold;")

                self.logger.info(f"項目訂閱完成: {success_count}/{subscribe_count} 成功")

                # 更新所有商品狀態
                for symbol in all_items.keys():
                    self.multi_auto_manager.update_symbol_state(symbol, 'connected')

                # 更新最终狀態顯示
                if success_count > 0:
                    self.subscribe_btn.setText("取消訂閱")
                    self.connect_btn.setText("退訂並斷線")
                    self.status_label.setText(f"測試: {test_success_count}/{test_count}, 訂閱: {success_count}/{subscribe_count}")
                    self.status_label.setStyleSheet("color: blue; font-weight: bold;")

                # 通知自动管理器連接完成 - 只通知第一個商品，避免重複處理
                if hasattr(self, 'multi_auto_manager') and self.multi_auto_manager:
                    # 只通知第一個商品，表示所有商品都已處理完成
                    first_symbol = list(all_items.keys())[0] if all_items else None
                    if first_symbol:
                        self.multi_auto_manager.notify_connection_completed(first_symbol, success_count > 0)

            else:
                self.logger.error("DDE客戶端未連接，無法自動訂閱")
                # 通知自动管理器连接失败 - 只通知第一個商品
                if hasattr(self, 'multi_auto_manager') and self.multi_auto_manager:
                    all_items = self.multi_config.get_all_symbol_items()
                    first_symbol = list(all_items.keys())[0] if all_items else None
                    if first_symbol:
                        self.multi_auto_manager.notify_connection_completed(first_symbol, False)

        except Exception as e:
            self.logger.error(f"自動測試並訂閱所有商品失敗: {str(e)}")
            # 通知自动管理器连接失败 - 只通知第一個商品
            if hasattr(self, 'multi_auto_manager') and self.multi_auto_manager:
                all_items = self.multi_config.get_all_symbol_items()
                first_symbol = list(all_items.keys())[0] if all_items else None
                if first_symbol:
                    self.multi_auto_manager.notify_connection_completed(first_symbol, False)

    def auto_subscribe_symbol(self, symbol: str):
        """自动測試並訂閱指定商品 - 重定向到全部商品處理"""
        # 為了保持兼容性，將單個商品的請求重定向到全部商品處理
        # 這樣可以避免商品切換導致的DDE衝突問題
        self.logger.info(f"收到商品 {symbol} 的自動連線請求，將處理所有商品")
        self.auto_subscribe_all_symbols()

    def auto_disconnect_symbol(self, symbol: str):
        """自动断开指定商品"""
        try:
            self.logger.info(f"自动断开商品: {symbol}")

            if self.dde_client:
                # 取消訂閱该商品的所有項目
                symbol_items = {}
                all_items = self.multi_config.get_all_symbol_items()
                if symbol in all_items:
                    symbol_items = all_items[symbol]

                for data_type, items in symbol_items.items():
                    for item_code, item_name in items.items():
                        try:
                            self.dde_client.unadvise(item_code)
                            self.update_item_in_table(symbol, data_type, item_code, "", "自動取消")
                        except Exception as e:
                            self.logger.error(f"自動取消訂閱失敗 {item_code}: {str(e)}")

                # 更新商品狀態
                self.multi_auto_manager.update_symbol_state(symbol, 'disconnected')
                self.logger.info(f"商品 {symbol} 自动断开完成")

                # 检查是否所有商品都已取消订阅，如果是则更新按钮状态
                self.check_and_update_button_states()

        except Exception as e:
            self.logger.error(f"自动断开商品失敗 {symbol}: {str(e)}")

    def auto_unadvise_symbol(self, symbol: str):
        """自动取消訂閱指定商品（不断开連接）"""
        try:
            self.logger.info(f"自动取消訂閱商品: {symbol}")

            if self.dde_client:
                # 取消訂閱该商品的所有項目
                symbol_items = {}
                all_items = self.multi_config.get_all_symbol_items()
                if symbol in all_items:
                    symbol_items = all_items[symbol]

                for data_type, items in symbol_items.items():
                    for item_code, item_name in items.items():
                        try:
                            self.dde_client.unadvise(item_code)
                            self.update_item_in_table(symbol, data_type, item_code, "", "自動結束")
                        except Exception as e:
                            self.logger.error(f"自動取消訂閱失敗 {item_code}: {str(e)}")

                # 更新商品狀態
                self.multi_auto_manager.update_symbol_state(symbol, 'ended')
                self.logger.info(f"商品 {symbol} 自动取消訂閱完成")

                # 检查是否所有商品都已取消订阅，如果是则更新按钮状态
                self.check_and_update_button_states()

        except Exception as e:
            self.logger.error(f"自动取消訂閱商品失敗 {symbol}: {str(e)}")

    def check_and_update_button_states(self):
        """检查所有商品状态并更新按钮状态"""
        try:
            if not self.dde_client or not self.dde_client.is_connected():
                return

            # 检查是否还有已订阅的商品
            has_subscribed_items = False

            # 遍历所有表格检查状态
            for symbol in self.multi_config.get_symbols():
                if symbol in self.symbol_tables:
                    table = self.symbol_tables[symbol]
                    for row in range(table.rowCount()):
                        status_item = table.item(row, 3)  # 状态列
                        if status_item and status_item.text() == "已訂閱":
                            has_subscribed_items = True
                            break
                if has_subscribed_items:
                    break

            # 根据订阅状态更新按钮
            if has_subscribed_items:
                self.subscribe_btn.setText("取消訂閱")
                self.connect_btn.setText("退訂並斷線")
            else:
                self.subscribe_btn.setText("訂閱項目")
                self.connect_btn.setText("連線並訂閱")

        except Exception as e:
            self.logger.error(f"检查并更新按钮状态失败: {str(e)}")

    def auto_shutdown(self):
        """自動結束程式"""
        try:
            self.logger.info("触发自動結束程式")
            self._auto_shutdown_in_progress = True

            # 保存数据並清理资源
            self.cleanup()

            # 关闭程式
            QApplication.quit()

        except Exception as e:
            self.logger.error(f"自動結束程式失敗: {str(e)}")

    def update_auto_status_display(self, status_message: str = ""):
        """更新自動化狀態顯示"""
        try:
            if not status_message:
                # 获取所有商品的狀態
                all_status = self.multi_auto_manager.get_all_status()

                connected_count = 0
                auto_enabled_count = 0

                for symbol, status in all_status.items():
                    if status['enable_auto_connect']:
                        auto_enabled_count += 1
                    if status['current_state'] == 'connected':
                        connected_count += 1

                status_text = f"🔄 自動管理: {auto_enabled_count}商品 | 🔗 已連接: {connected_count}商品"

                if self.multi_auto_manager.enable_auto_shutdown:
                    status_text += f" | ⏰ 自動結束: {self.multi_auto_manager.shutdown_time_str}"

                self.auto_status_label.setText(status_text)
            else:
                self.auto_status_label.setText(status_message)

        except Exception as e:
            self.logger.error(f"更新自動化狀態顯示失敗: {str(e)}")

    def show_auto_settings(self):
        """顯示自動化设定对话框"""
        try:
            # 顯示多商品自動化狀態信息
            all_status = self.multi_auto_manager.get_all_status()

            status_text = "多商品自動化狀態:\n\n"
            for symbol, status in all_status.items():
                status_text += f"商品: {symbol}\n"
                status_text += f"  自動連接: {'啟用' if status['enable_auto_connect'] else '禁用'}\n"
                status_text += f"  連接模式: {status['auto_connect_mode']}\n"
                status_text += f"  當前狀態: {status['current_state']}\n"
                if status['schedule_times']:
                    times_str = ', '.join([f"{start}-{end}" for start, end in status['schedule_times']])
                    status_text += f"  時間表: {times_str}\n"
                if status['next_connect_time']:
                    status_text += f"  下次連接: {status['next_connect_time']}\n"
                status_text += "\n"

            QMessageBox.information(self, "多商品自動化狀態", status_text)

        except Exception as e:
            self.logger.error(f"顯示自動化设定失敗: {str(e)}")

    def setup_log_handler(self):
        """设置日誌处理器"""
        try:
            # 创建自定义日誌处理器
            class GuiLogHandler(logging.Handler):
                def __init__(self, text_widget, parent):
                    super().__init__()
                    self.text_widget = text_widget
                    self.parent = parent

                def emit(self, record):
                    if not self.parent.log_paused:
                        msg = self.format(record)
                        # 在GUI线程中更新文本
                        self.text_widget.append(msg)
                        # 自动滚动到底部
                        cursor = self.text_widget.textCursor()
                        cursor.movePosition(QTextCursor.MoveOperation.End)
                        self.text_widget.setTextCursor(cursor)

            # 添加到根日誌记录器
            self.gui_log_handler = GuiLogHandler(self.log_text, self)
            formatter = logging.Formatter('%(asctime)s [%(levelname)s] %(message)s',
                                        datefmt='%H:%M:%S')
            self.gui_log_handler.setFormatter(formatter)

            # 获取根日誌记录器並添加处理器
            root_logger = logging.getLogger()
            root_logger.addHandler(self.gui_log_handler)

        except Exception as e:
            print(f"设置日誌处理器失敗: {str(e)}")

    def toggle_log_pause(self):
        """切換日誌暂停狀態"""
        try:
            self.log_paused = not self.log_paused
            if self.log_paused:
                self.log_pause_btn.setText("繼續")
                self.log_pause_btn.setStyleSheet("background-color: orange;")
            else:
                self.log_pause_btn.setText("暫停")
                self.log_pause_btn.setStyleSheet("")

        except Exception as e:
            self.logger.error(f"切換日誌暂停狀態失敗: {str(e)}")

    def clear_log(self):
        """清除日誌"""
        try:
            self.log_text.clear()
            self.logger.info("日誌已清除")

        except Exception as e:
            self.logger.error(f"清除日誌失敗: {str(e)}")

    def cleanup(self):
        """清理资源"""
        try:
            # 停止自動化管理器
            if self.multi_auto_manager:
                self.multi_auto_manager.stop()

            # 停止数据处理
            if self.multi_data_processor:
                self.multi_data_processor.stop()

            if self.process_thread and self.process_thread.isRunning():
                self.process_thread.quit()
                self.process_thread.wait()

            # 断开DDE連接
            if self.dde_client:
                self.disconnect_service()

            # 停止资源监控
            if self.resource_monitor:
                self.resource_monitor.stop_monitoring()

            self.logger.info("多商品主窗口资源清理完成")

        except Exception as e:
            self.logger.error(f"清理资源失敗: {str(e)}")

    def closeEvent(self, event):
        """窗口关闭事件"""
        try:
            self.cleanup()
            event.accept()

        except Exception as e:
            self.logger.error(f"窗口关闭处理失敗: {str(e)}")
            event.accept()
