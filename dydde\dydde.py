"""
DDE Module
==========

Windows DDE (Dynamic Data Exchange) 客戶端和服務器的 Python 實現。

設計原則：
---------
1. 同時支援 GUI 和非 GUI 應用
   - 使用非阻塞的消息循環
   - 支援異步操作
   - 資源管理獨立於 GUI 框架

2. 完整的錯誤處理和資源管理
   - 自動資源清理
   - 詳細的錯誤信息
   - 異常恢復機制

3. 統一的編碼處理
   - 支援 CP950/Big5/UTF-16-LE
   - 自動編碼轉換
   - 錯誤容錯處理

Classes:
    DDEClient: DDE 客戶端類，用於連接 DDE 服務器並接收/發送數據
    DDEServer: DDE 服務器類，用於提供 DDE 服務
    DDEError: DDE 錯誤異常類
"""

import win32ui
#import dde
import time
import sys
import logging
import pythoncom
import asyncio
from concurrent.futures import ThreadPoolExecutor
from typing import Dict, Any, Optional, Callable, List
from ctypes import (POINTER, WINFUNCTYPE, c_char_p, c_void_p, c_int, c_ulong, c_uint,
                   c_byte, windll, byref, create_string_buffer, create_unicode_buffer, 
                   cast, string_at, sizeof, Structure)
from ctypes.wintypes import (BOOL, HWND, MSG, DWORD, BYTE, INT, LPCWSTR, UINT, 
                           ULONG, LPCSTR, LPSTR, LPWSTR, LPARAM, WPARAM)
from threading import Event
from collections import deque
from datetime import datetime, timedelta
import threading
from PySide6.QtCore import QTimer

# DDE 常量和類型定義
HCONV = c_void_p
HDDEDATA = c_void_p
HSZ = c_void_p
LPBYTE = c_char_p
LPDWORD = POINTER(DWORD)
ULONG_PTR = c_ulong
PCONVINFO = c_void_p
PCONVCONTEXT = c_void_p
LPMSG = POINTER(MSG)
LRESULT = c_ulong
DWORD_PTR = ULONG_PTR
HCONVLIST = c_void_p
LPVOID = c_void_p

# DDE 常量
# DDEML errors
DMLERR_NO_ERROR             = 0x0000  # No error
DMLERR_ADVACKTIMEOUT        = 0x4000  # request for synchronous advise transaction timed out
DMLERR_BUSY                 = 0x4001
DMLERR_DATAACKTIMEOUT       = 0x4002  # request for synchronous data transaction timed out
DMLERR_DLL_NOT_INITIALIZED  = 0x4003  # DDEML functions called without iniatializing
DMLERR_DLL_USAGE            = 0x4004
DMLERR_EXECACKTIMEOUT       = 0x4005  # request for synchronous execute transaction timed out
DMLERR_INVALIDPARAMETER     = 0x4006
DMLERR_LOW_MEMORY           = 0x4007
DMLERR_MEMORY_ERROR         = 0x4008
DMLERR_NOTPROCESSED         = 0x4009
DMLERR_NO_CONV_ESTABLISHED  = 0x400a  # client's attempt to establish a conversation has failed
DMLERR_POKEACKTIMEOUT       = 0x400b  # A request for a synchronous poke transaction has timed out
DMLERR_POSTMSG_FAILED       = 0x400c  # An internal call to the PostMessage function has failed
DMLERR_REENTRANCY           = 0x400d
DMLERR_SERVER_DIED          = 0x400e
DMLERR_SYS_ERROR            = 0x400f
DMLERR_UNADVACKTIMEOUT      = 0x4010
DMLERR_UNFOUND_QUEUE_ID     = 0x4011

# Predefined Clipboard Formats
CF_TEXT         =  1
CF_BITMAP       =  2
CF_METAFILEPICT =  3
CF_SYLK         =  4
CF_DIF          =  5
CF_TIFF         =  6
CF_OEMTEXT      =  7
CF_DIB          =  8
CF_PALETTE      =  9
CF_PENDATA      = 10
CF_RIFF         = 11
CF_WAVE         = 12
CF_UNICODETEXT  = 13
CF_ENHMETAFILE  = 14
CF_HDROP        = 15
CF_LOCALE       = 16
CF_DIBV5        = 17
CF_MAX          = 18

# DDE constants for wStatus field
DDE_FACK          = 0x8000
DDE_FBUSY         = 0x4000
DDE_FDEFERUPD     = 0x4000
DDE_FACKREQ       = 0x8000
DDE_FRELEASE      = 0x2000
DDE_FREQUESTED    = 0x1000
DDE_FAPPSTATUS    = 0x00FF
DDE_FNOTPROCESSED = 0x0000

DDE_FACKRESERVED  = (~(DDE_FACK | DDE_FBUSY | DDE_FAPPSTATUS))
DDE_FADVRESERVED  = (~(DDE_FACKREQ | DDE_FDEFERUPD))
DDE_FDATRESERVED  = (~(DDE_FACKREQ | DDE_FRELEASE | DDE_FREQUESTED))
DDE_FPOKRESERVED  = (~(DDE_FRELEASE))

# DDEML Transaction class flags
XTYPF_NOBLOCK        = 0x0002
XTYPF_NODATA         = 0x0004
XTYPF_ACKREQ         = 0x0008

XCLASS_MASK          = 0xFC00
XCLASS_BOOL          = 0x1000
XCLASS_DATA          = 0x2000
XCLASS_FLAGS         = 0x4000
XCLASS_NOTIFICATION  = 0x8000

XTYP_ERROR           = (0x0000 | XCLASS_NOTIFICATION | XTYPF_NOBLOCK)
XTYP_ADVDATA         = (0x0010 | XCLASS_FLAGS)
XTYP_ADVREQ          = (0x0020 | XCLASS_DATA | XTYPF_NOBLOCK)
XTYP_ADVSTART        = (0x0030 | XCLASS_BOOL)
XTYP_ADVSTOP         = (0x0040 | XCLASS_NOTIFICATION)
XTYP_EXECUTE         = (0x0050 | XCLASS_FLAGS)
XTYP_CONNECT         = (0x0060 | XCLASS_BOOL | XTYPF_NOBLOCK)
XTYP_CONNECT_CONFIRM = (0x0070 | XCLASS_NOTIFICATION | XTYPF_NOBLOCK)
XTYP_XACT_COMPLETE   = (0x0080 | XCLASS_NOTIFICATION)
XTYP_POKE            = (0x0090 | XCLASS_FLAGS)
XTYP_REGISTER        = (0x00A0 | XCLASS_NOTIFICATION | XTYPF_NOBLOCK)
XTYP_REQUEST         = (0x00B0 | XCLASS_DATA)
XTYP_DISCONNECT      = (0x00C0 | XCLASS_NOTIFICATION | XTYPF_NOBLOCK)
XTYP_UNREGISTER      = (0x00D0 | XCLASS_NOTIFICATION | XTYPF_NOBLOCK)
XTYP_WILDCONNECT     = (0x00E0 | XCLASS_DATA | XTYPF_NOBLOCK)
XTYP_MONITOR         = (0x00F0 | XCLASS_NOTIFICATION | XTYPF_NOBLOCK)

XTYP_MASK            = 0x00F0
XTYP_SHIFT           = 4
# 添加在常量定義區域
XTYP_PING = 0x0048  # 定義 PING 交易類型

# DDE Timeout constants
TIMEOUT_ASYNC        = 0xFFFFFFFF
TIMEOUT_SYNC         = 0x0000

# DDE Initialization flags
APPCLASS_STANDARD    = 0x00000000
APPCLASS_MASK        = 0x0000000F
APPCLASS_MONITOR     = 0x00000001
APPCMD_CLIENTONLY    = 0x00000010
APPCMD_FILTERINITS   = 0x00000020
APPCMD_MASK          = 0x00000FF0

# Callback filter flags
CBF_FAIL_SELFCONNECTIONS    = 0x00001000
CBF_FAIL_CONNECTIONS        = 0x00002000
CBF_FAIL_ADVISES            = 0x00004000
CBF_FAIL_EXECUTES           = 0x00008000
CBF_FAIL_POKES              = 0x00010000
CBF_FAIL_REQUESTS           = 0x00020000
CBF_FAIL_ALLSVRXACTIONS     = 0x0003f000
CBF_SKIP_CONNECT_CONFIRMS   = 0x00040000
CBF_SKIP_REGISTRATIONS      = 0x00080000
CBF_SKIP_UNREGISTRATIONS    = 0x00100000
CBF_SKIP_DISCONNECTS        = 0x00200000
CBF_SKIP_ALLNOTIFICATIONS   = 0x003c0000

# Code page for rendering string
CP_WINANSI      = 1004    # default codepage for windows & old DDE convs.
CP_WINUNICODE   = 1200

# Message flags
PM_NOREMOVE = 0x0000
PM_REMOVE   = 0x0001
PM_NOYIELD  = 0x0002

# DDE 消息常量
WM_DDE_FIRST        = 0x03E0
WM_DDE_INITIATE     = (WM_DDE_FIRST)
WM_DDE_TERMINATE    = (WM_DDE_FIRST+1)
WM_DDE_ADVISE       = (WM_DDE_FIRST+2)
WM_DDE_UNADVISE     = (WM_DDE_FIRST+3)
WM_DDE_ACK          = (WM_DDE_FIRST+4)
WM_DDE_DATA         = (WM_DDE_FIRST+5)
WM_DDE_REQUEST      = (WM_DDE_FIRST+6)
WM_DDE_POKE         = (WM_DDE_FIRST+7)
WM_DDE_EXECUTE      = (WM_DDE_FIRST+8)
WM_DDE_LAST         = (WM_DDE_FIRST+8)

# 回調函數類型
DDEML_CALLBACK_TYPE = WINFUNCTYPE(
    HDDEDATA,  # 返回類型
    UINT,      # uType
    UINT,      # uFmt
    HCONV,     # hconv
    HSZ,       # hsz1
    HSZ,       # hsz2
    HDDEDATA,  # hdata
    ULONG_PTR, # dwData1
    ULONG_PTR  # dwData2
)

# DDE 超時常量
DDE_TIMEOUT = 5000  # 5 秒

# 添加 DNS 常量
DNS_REGISTER    = 0x0001
DNS_UNREGISTER  = 0x0002
DNS_FILTERON    = 0x0004
DNS_FILTEROFF   = 0x0008

#COINIT
COINIT_APARTMENTTHREADED = 0x2
COINIT_MULTITHREADED     = 0x0
COINIT_DISABLE_OLE1DDE   = 0x4
COINIT_SPEED_OVER_MEMORY = 0x8


# 添加在常量定義區域
QID_SYNC = 0x00000000
QID_ASYNC = 0x00000001




# 添加 SECURITY_QUALITY_OF_SERVICE 結構定義
class SECURITY_QUALITY_OF_SERVICE(Structure):
    _fields_ = [
        ("Length", DWORD),
        ("ImpersonationLevel", DWORD),
        ("ContextTrackingMode", BYTE),
        ("EffectiveOnly", BOOL)
    ]

# 添加 CONVCONTEXT 結構定義
class CONVCONTEXT(Structure):
    _fields_ = [
        ("cb", UINT),              # 結構大小
        ("wFlags", UINT),          # 標誌
        ("wCountryID", UINT),      # 國家/地區 ID
        ("iCodePage", INT),        # 代碼頁
        ("dwLangID", DWORD),       # 語言 ID
        ("dwSecurity", DWORD),     # 安全選項
        ("qos", SECURITY_QUALITY_OF_SERVICE)  # 服務質量
    ]

# 添加 CONVINFO 結構定義
class CONVINFO(Structure):
    _fields_ = [
        ("cb", DWORD),              # 結構大小
        ("hUser", DWORD_PTR),      # 用戶定義的值
        ("hConvPartner", HCONV),   # 伙伴的對話句柄
        ("hszSvcPartner", HSZ),    # 伙伴的服務名稱
        ("hszServiceReq", HSZ),    # 請求的服務名稱
        ("hszTopic", HSZ),         # 主題名稱
        ("hszItem", HSZ),          # 項目名稱
        ("wFmt", UINT),            # 格式
        ("wType", UINT),           # 交易類型
        ("wStatus", UINT),         # 狀態標誌
        ("wConvst", UINT),         # 對話狀態
        ("wLastError", UINT),      # 最後錯誤碼
        ("hConvList", HCONVLIST),  # 對話列表句柄
        ("ConvCtxt", CONVCONTEXT), # 對話上下文
        ("hwnd", HWND),           # 窗口句柄
        ("hwndPartner", HWND)     # 伙伴窗口句柄
    ]


"""
# 定義 CONVINFO 結構
class CONVINFO(Structure):
    _fields_ = [
        ("cb", c_ulong),          # 結構大小
        ("hUser", c_void_p),     # 用戶數據句柄
        ("hConvPartner", c_void_p), # 對話夥伴句柄
        ("hszSvcPartner", c_void_p), # 對話夥伴的服務句柄
        ("hszTopic", c_void_p),      # 主題句柄
        ("hszItem", c_void_p),       # 項目句柄
        ("wFmt", c_uint),        # 格式
        ("wType", c_uint),       # 類型
        ("wStatus", c_uint),     # 狀態
        ("wConvst", c_uint),     # 會話狀態
        ("wLastError", c_uint),  # 最後的錯誤
        ("hConvList", c_void_p), # 對話列表句柄
        ("ConvCtxt", c_uint),    # 對話上下文
        ("szConvst", c_uint),    # 保留
        ("dwStartTime", c_ulong),# 開始時間
        ("dwLastAccessTime", c_ulong), # 最後訪問時間
    ]

"""


# 錯誤類
class DDEError(Exception):
    """DDE 錯誤基類"""
    def __init__(self, msg: str, idInst: Optional[DWORD] = None, 
                 error_code: Optional[int] = None):
        self.idInst = idInst
        self.error_code = error_code
        if error_code is None and idInst is not None:
            self.error_code = DDE.GetLastError(idInst)
            
        error_desc = self._get_error_description()
        super().__init__(f"{msg} ({error_desc})")
        
    def _get_error_description(self) -> str:
        """獲取詳細的錯誤描述"""
        if self.error_code is None:
            return "未知錯誤"
            
        error_map = {
                DMLERR_ADVACKTIMEOUT: "同步建議事務超時",
                DMLERR_BUSY: "系統忙碌",
                DMLERR_DATAACKTIMEOUT: "同步數據事務超時",
                DMLERR_DLL_NOT_INITIALIZED: "DDEML 未初始化",
                DMLERR_DLL_USAGE: "DLL 使用錯誤",
                DMLERR_EXECACKTIMEOUT: "同步執行事務超時",
                DMLERR_INVALIDPARAMETER: "無效參數",
                DMLERR_LOW_MEMORY: "內存不足",
                DMLERR_MEMORY_ERROR: "內存錯誤",
                DMLERR_NOTPROCESSED: "未處理",
                DMLERR_NO_CONV_ESTABLISHED: "無法建立會話",
                DMLERR_POKEACKTIMEOUT: "同步 POKE 事務超時",
                DMLERR_POSTMSG_FAILED: "PostMessage 調用失敗",
                DMLERR_REENTRANCY: "重入錯誤",
                DMLERR_SERVER_DIED: "服務器已終止",
                DMLERR_SYS_ERROR: "系統錯誤",
                DMLERR_UNADVACKTIMEOUT: "同步取消建議事務超時",
                DMLERR_UNFOUND_QUEUE_ID: "未找到隊列 ID"
        }
        
        desc = error_map.get(self.error_code, "未知錯誤")
        return f"錯誤代碼: 0x{self.error_code:x} - {desc}"

class DDEClientError(DDEError):
    """DDE 客戶端錯誤"""
    pass

class DDEServerError(DDEError):
    """DDE 服務器錯誤"""
    pass

class DDEConfig:
    """DDE 配置類"""
    def __init__(self, **kwargs):
        self.timeout = kwargs.get('timeout', 5000)
        self.max_retries = kwargs.get('max_retries', 3)
        self.retry_interval = kwargs.get('retry_interval', 1.0)
        self.log_level = kwargs.get('log_level', logging.INFO)
        self.encoding = kwargs.get('encoding', 'cp950')
        self.max_data_size = kwargs.get('max_data_size', 1024 * 1024)
        self.enable_async = kwargs.get('enable_async', True)
        self.connection_check_interval = kwargs.get('connection_check_interval', 5.0)

# DDE 基礎功能類
class DDE:
    """DDE 基礎功能類"""
    @staticmethod
    def get_winfunc(libname: str, funcname: str, restype: Optional[Any] = None, 
                    argtypes: tuple = (), _libcache: Dict[str, Any] = {}) -> Callable:
        """獲取 Windows API 函數"""
        if libname not in _libcache:
            _libcache[libname] = windll.LoadLibrary(libname)
        func = getattr(_libcache[libname], funcname)
        func.argtypes = argtypes
        func.restype = restype
        return func

    # DDE 函數
    AccessData          = get_winfunc("user32", "DdeAccessData", LPBYTE, (HDDEDATA, LPDWORD))
    ClientTransaction   = get_winfunc("user32", "DdeClientTransaction",
                            HDDEDATA,
                            (LPBYTE, DWORD, HCONV, HSZ, UINT, UINT, DWORD, LPDWORD))
    Connect             = get_winfunc("user32", "DdeConnect", HCONV, (DWORD, HSZ, HSZ, PCONVCONTEXT))
    CreateStringHandle  = get_winfunc("user32", "DdeCreateStringHandleW", HSZ, (DWORD, LPCWSTR, UINT))
    Disconnect          = get_winfunc("user32", "DdeDisconnect", BOOL, (HCONV,))
    GetLastError        = get_winfunc("user32", "DdeGetLastError", UINT, (DWORD,))
    Initialize          = get_winfunc("user32", "DdeInitializeW", UINT, 
                            (LPDWORD, DDEML_CALLBACK_TYPE, DWORD, DWORD))
    FreeDataHandle      = get_winfunc("user32", "DdeFreeDataHandle", BOOL, (HDDEDATA,))
    FreeStringHandle    = get_winfunc("user32", "DdeFreeStringHandle", BOOL, (DWORD, HSZ))
    QueryString         = get_winfunc("user32", "DdeQueryStringW", DWORD, 
                            (DWORD, HSZ, LPWSTR, DWORD, c_int))
    UnaccessData        = get_winfunc("user32", "DdeUnaccessData", BOOL, (HDDEDATA,))
    Uninitialize        = get_winfunc("user32", "DdeUninitialize", BOOL, (DWORD,))
    GetData             = get_winfunc("user32", "DdeGetData", DWORD, (HDDEDATA, LPBYTE, DWORD, DWORD))
    CreateDataHandle    = get_winfunc("user32", "DdeCreateDataHandle", HDDEDATA, 
                            (DWORD, LPBYTE, DWORD, DWORD, HSZ, UINT, UINT))
    NameService         = get_winfunc("user32", "DdeNameService", HDDEDATA, (DWORD, HSZ, HSZ, UINT))
    PostMessage         = get_winfunc("user32", "PostMessageW", BOOL, (HWND, UINT, WPARAM, LPARAM))
    SendMessage         = get_winfunc("user32", "SendMessageW", LRESULT, (HWND, UINT, WPARAM, LPARAM))

    # Windows 消息函數
    GetMessage          = get_winfunc("user32", "GetMessageW", BOOL, (LPMSG, HWND, UINT, UINT))
    TranslateMessage    = get_winfunc("user32", "TranslateMessage", BOOL, (LPMSG,))
    DispatchMessage     = get_winfunc("user32", "DispatchMessageW", LRESULT, (LPMSG,))
    PeekMessage         = get_winfunc("user32", "PeekMessageW", BOOL, 
                             (LPMSG, HWND, UINT, UINT, UINT))

    # 添加 DdePostAdvise 函數定義
    PostAdvise          = get_winfunc("user32", "DdePostAdvise", BOOL, (DWORD, HSZ, HSZ))

    # 添加 QueryConvInfo 函數定義
    QueryConvInfo = get_winfunc("user32", "DdeQueryConvInfo", UINT, 
                               (HCONV, DWORD, POINTER(CONVINFO)))

    # 添加 DdeReconnect 函數定義
    Reconnect = get_winfunc("user32", "DdeReconnect", HCONV, (HCONV,))

    @classmethod
    def get_data(cls, hDdeData: HDDEDATA) -> Optional[bytes]:
        """從 DDE 數據句柄獲取數據"""
        if not hDdeData:
            return None
        
        size = cls.GetData(hDdeData, None, 0, 0)
        if not size:
            raise DDEError("無法獲取數據大小")
        
        buffer = create_string_buffer(size)
        result = cls.GetData(hDdeData, buffer, size, 0)
        if result != size:
            raise DDEError(f"獲取數據失敗: 預期 {size} 字節，實際獲得 {result} 字節")
        
        return buffer.raw

    @classmethod
    def query_string(cls, idInst: DWORD, hsz: HSZ) -> str:
        """字符串句柄獲取字符串"""
        if not hsz:
            return ""
        i = cls.QueryString(idInst, hsz, None, 0, CP_WINANSI)
        if i:
            i += 1
            buffer = create_unicode_buffer(i)
            cls.QueryString(idInst, hsz, buffer, i, CP_WINUNICODE)
            return buffer.value
        return ""

    @classmethod
    def _create_data_handle(cls, idInst: DWORD, data: bytes, hsz_item: HSZ) -> HDDEDATA:
        """創建數據句柄的輔助方法"""
        if data is None:
            len_data = 0
        else:
            len_data = len(data)
            
        return cls.CreateDataHandle(
            idInst,
            data,
            len_data,
            0,
            hsz_item,
            CF_TEXT,
            0
        )

    @classmethod
    def _create_data_handle0(cls, idInst: DWORD, data: bytes, len_data, cbOff, hszItem: HSZ, wFmt, afCmd) -> HDDEDATA:
        """創建數據句柄的輔助方法"""
        #if data is None:
        #    len_data = 0
        #else:
        #    len_data = len(data)
            
        return cls.CreateDataHandle(
            idInst,         # idInst
            data,           # pSrc
            len_data,       # cb
            cbOff,          # cbOff
            hszItem,        # hszItem
            wFmt,           # wFmt
            afCmd           # afCmd
        )                   # https://learn.microsoft.com/en-us/windows/win32/api/ddeml/nf-ddeml-ddecreatedatahandle

    '''
    @staticmethod
    def ClientTransaction(pData: Optional[bytes], cbData: int, hConv: HCONV, 
                         hsz1: Optional[HSZ], wFmt: int, wType: int, 
                         dwTimeout: int, pdwResult: Optional[LPDWORD]) -> HDDEDATA:
        """執行 DDE 事務"""
        func = DDE.get_winfunc("user32", "DdeClientTransaction", HDDEDATA,
                              (LPBYTE, DWORD, HCONV, HSZ, UINT, UINT, DWORD, LPDWORD, HDDEDATA))
        return func(pData, cbData, hConv, hsz1, wFmt, wType, dwTimeout, pdwResult)
    '''

    @staticmethod
    def PostAdvise(idInst: int, hsz1: int, hsz2: int) -> bool:
        """發送數據更新通知"""
        return windll.user32.DdePostAdvise(
            idInst, hsz1, hsz2
        )

class DDEBase:
    """DDE 基礎類，供共用功能"""
    def __init__(self, config: Optional[DDEConfig] = None):
        self.config = config or DDEConfig()
        self.logger = logging.getLogger(self.__class__.__name__)
        self._running = False
        self._connected = False
        self._performance_metrics = {
            'request_times': deque(maxlen=100),
            'execute_times': deque(maxlen=100),
            'update_times': deque(maxlen=100)
        }
        self._connection_status = {
            'last_check_time': 0,
            'check_interval': self.config.connection_check_interval,  # 5 秒檢查一次
            'is_connected': False,
            'reconnect_attempts': 0,
            'last_error': None
        }
        self._event_handlers = {
            'connect': [],
            'disconnect': [],
            'error': [],
            'data_update': []
        }
        self._resources_lock = threading.RLock()
        self._resources = []
        self._handles = set()  # 追蹤所有句柄
        self._is_disposing = False
        self._setup_error_handling()
        
    def _setup_logger(self, log_level: int = logging.INFO) -> logging.Logger:
        """設置日誌記錄器"""
        logger = logging.getLogger(self.__class__.__name__)
        logger.setLevel(log_level)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        return logger

    def _record_metric(self, operation: str, duration: float) -> None:
        """記錄操作耗時"""
        if operation in self._performance_metrics:
            self._performance_metrics[operation].append(duration)

    def get_performance_stats(self) -> Dict[str, Any]:
        """獲取性能統計信息"""
        stats = {}
        for op, times in self._performance_metrics.items():
            if times:
                stats[op] = {
                    'avg': sum(times) / len(times),
                    'min': min(times),
                    'max': max(times),
                    'count': len(times)
                }
        return stats

    def get_connection_status(self) -> Dict[str, Any]:
        """獲取連接狀態信息"""
        return {
            'is_connected': self._connection_status['is_connected'],
            'last_check_time': self._connection_status['last_check_time'],
            'reconnect_attempts': self._connection_status['reconnect_attempts'],
            'last_error': str(self._connection_status['last_error']) if self._connection_status['last_error'] else None
        }

    def add_event_handler(self, event_type: str, handler: Callable) -> None:
        """註冊事件處理器"""
        if event_type in self._event_handlers:
            self._event_handlers[event_type].append(handler)

    def remove_event_handler(self, event_type: str, handler: Callable) -> None:
        """移除事件處理器"""
        if event_type in self._event_handlers:
            self._event_handlers[event_type].remove(handler)

    def _notify_event(self, event_type: str, **kwargs) -> None:
        """觸發事件通知"""
        if event_type in self._event_handlers:
            for handler in self._event_handlers[event_type]:
                try:
                    handler(**kwargs)
                except Exception as e:
                    self.logger.error(f"事件處理器異常: {str(e)}")

    def _track_resource(self, resource: Any, cleanup_func: Callable, 
                       description: str = "") -> None:
        """追蹤需要清理的資源
        
        Args:
            resource: 要追蹤的資源
            cleanup_func: 清理函數
            description: 資源描述,用於日誌
        """
        with self._resources_lock:
            self._resources.append({
                'resource': resource,
                'cleanup': cleanup_func,
                'description': description,
                'created_at': datetime.now()
            })
            if isinstance(resource, (HSZ, HDDEDATA, HCONV)):
                self._handles.add(resource)
            self.logger.debug(f"已追蹤資源: {description}")

    def _cleanup_resources(self) -> None:
        """清理資源"""
        print(f'[{timestamp()}] [DEBUG] [C] _cleanup_resources 開始')
        try:
            # 清理所有訂閱
            for item in list(self._advise_items.keys()):
                print(f'[{timestamp()}] [DEBUG] [C] 清理訂閱: {item}')
                self.unadvise(item)
                
            # 清理所有回調
            self._callbacks.clear()
            print(f'[{timestamp()}] [DEBUG] [C] 清理回調完成')
            
            # 清理所有會話
            self._conversations.clear()
            print(f'[{timestamp()}] [DEBUG] [C] 清理會話完成')
            
            # 清理所有句柄
            if hasattr(self, '_handles'):
                for handle in self._handles:
                    try:
                        if handle and handle.value:
                            if isinstance(handle, HSZ):
                                print(f'[{timestamp()}] [DEBUG] [C] 釋放字串句柄: {handle}')
                                DDE.FreeStringHandle(self.idInst, handle)
                            elif isinstance(handle, HDDEDATA):
                                print(f'[{timestamp()}] [DEBUG] [C] 釋放資料句柄: {handle}')
                                DDE.FreeDataHandle(handle)
                            # 不執行 DDE.Disconnect，避免影響其他程式
                    except Exception as e:
                        print(f'[{timestamp()}] [ERROR] [C] 清理句柄失敗: {str(e)}')
                self._handles.clear()
                print(f'[{timestamp()}] [DEBUG] [C] 清理句柄完成')
            
            print(f'[{timestamp()}] [DEBUG] [C] _cleanup_resources 完成')
            
        except Exception as e:
            print(f'[{timestamp()}] [ERROR] [C] 清理資源時發生錯誤: {str(e)}')

    def _setup_error_handling(self):
        """設置錯誤處理"""
        def error_handler(exc_type, exc_value, exc_traceback):
            if issubclass(exc_type, DDEError):
                # DDE 相關錯誤
                self.logger.error(f"DDE 錯誤: {str(exc_value)}")
                self._notify_event('error', 
                                 error=exc_value, 
                                 error_type='dde_error')
                
                # 特殊錯誤處理
                if exc_type == DDEClientError:
                    if isinstance(self, DDEClient):
                        self._handle_client_error(exc_value)
                elif exc_type == DDEServerError:
                    if isinstance(self, DDEServer):
                        self._handle_server_error(exc_value)
                        
            elif issubclass(exc_type, (WindowsError, OSError)):
                # Windows 系統錯誤
                self.logger.error(f"系統錯誤: {str(exc_value)}")
                self._notify_event('error', 
                                 error=exc_value, 
                                 error_type='system_error')
            else:
                # 其他未處理的異常
                self.logger.exception("dydde 未處理的異常")
                self._notify_event('error', 
                                 error=exc_value, 
                                 error_type='unknown_error')
        
        sys.excepthook = error_handler

    def _handle_client_error(self, error: DDEError):
        """處理客戶端特定錯誤"""
        if error.error_code in (DMLERR_NO_CONV_ESTABLISHED, 
                              DMLERR_SERVER_DIED):
            self.logger.info("檢測到連接斷開,嘗試重新連接...")
            self.ensure_connected()
            
    def _handle_server_error(self, error: DDEError):
        """處理服務器特定錯誤"""
        if error.error_code == DMLERR_DLL_NOT_INITIALIZED:
            self.logger.info("檢測到 DDE 未初始化,嘗試重新初始化...")
            self._initialize_dde()

    def log_debug(self, msg: str, *args, **kwargs):
        """調試日誌"""
        self.logger.debug(f"[{self.__class__.__name__}] {msg}", *args, **kwargs)

    def log_info(self, msg: str, *args, **kwargs):
        """信息日誌"""
        self.logger.info(f"[{self.__class__.__name__}] {msg}", *args, **kwargs)

    def log_warning(self, msg: str, *args, **kwargs):
        """警告日誌"""
        self.logger.warning(f"[{self.__class__.__name__}] {msg}", *args, **kwargs)

    def log_error(self, msg: str, *args, **kwargs):
        """錯誤日誌"""
        self.logger.error(f"[{self.__class__.__name__}] {msg}", *args, **kwargs)

    def _encode_data(self, data: str) -> bytes:
        """統一的編碼處理"""
        try:
            return data.encode('cp950') + b'\0'
        except UnicodeEncodeError:
            self.logger.warning("CP950 編碼失敗,嘗試 UTF-16-LE")
            return data.encode('utf-16-le') + b'\0\0'
        
    def _decode_data(self, data: bytes) -> str:
        """統一的解碼處理"""
        try:
            if isinstance(data, str):
                return data.rstrip('\0')
            return data.decode('cp950').rstrip('\0')
        except UnicodeDecodeError:
            self.logger.warning("CP950 解碼失敗,嘗試 UTF-16-LE")
            try:
                return data.decode('utf-16-le').rstrip('\0')
            except UnicodeDecodeError:
                self.logger.warning("UTF-16-LE 解碼失敗,嘗試 big5")
                return data.decode('big5', errors='ignore').rstrip('\0')

# 定義 DDE 回調函數類型
DDEML_CALLBACK_TYPE = WINFUNCTYPE(
    HDDEDATA,  # 返回類型
    UINT,      # uType
    UINT,      # uFmt
    HCONV,     # hconv
    HSZ,       # hsz1
    HSZ,       # hsz2
    HDDEDATA,  # hdata
    ULONG_PTR, # dwData1
    ULONG_PTR  # dwData2
)

class DDEClient(DDEBase):
    """DDE 客戶端類"""
    def __init__(self, service: str, topic: str, **options):
        """
        初始化 DDE 客戶端
        
        Args:
            service: 服務名稱
            topic: 主題名稱
            options: 可選配置
                auto_reconnect: bool = False  # 是否自動重連
                max_reconnect_attempts: int = 3  # 最大重連次數
                reconnect_interval: float = 1.0  # 重連間隔(秒)
                reconnect_callback: Callable = None  # 重連狀態回調
                health_check_item: Optional[str] = None  # 健康檢查項目
                check_interval: float = 5.0  # 健康檢查間隔
                health_check_fail_threshold: int = 3  # 健康檢查失敗閾值
                request_timeout: float = 5.0  # 請求超時時間
                min_reconnect_interval: float = 1.0  # 最小重連間隔
            service: DDE 服務名稱
            topic: DDE 主題名稱
            **options: 其他選項
                - disconnect_on_exit: 程式結束時是否斷開 DDE 連接 (預設: True)
        """
        super().__init__()
        self._service = service
        self._topic = topic
        self._hInst = None
        self._hConv = None
        self._advise_items = {}
        self._callbacks = {}
        self._options = options
        self.disconnect_on_exit = options.get('disconnect_on_exit', True)
        self._conversation_id = f"{service}.{topic}"  # 用於識別特定的連線
        
        # 斷線重連設定
        self._auto_reconnect = options.get('auto_reconnect', False)  # 預設關閉
        self._max_reconnect_attempts = options.get('max_reconnect_attempts', 3)
        self._reconnect_interval = options.get('reconnect_interval', 1.0)
        self._reconnect_callback = options.get('reconnect_callback', None)
        
        # 健康檢查設定
        self._health_check_item = options.get('health_check_item', None)
        self._check_interval = options.get('check_interval', 5.0)
        self._health_check_fail_threshold = options.get('health_check_fail_threshold', 3)
        
        # 內部狀態
        self._last_check_time = time.time()
        self._consecutive_failures = 0
        
        # 先初始化 logger
        self.logger = logging.getLogger(__name__)
        
        # 調用父類初始化
        super().__init__()
        
        self._initialized = False
        
        # 初始化 DDE
        self._initialize()
        
        self._subscriptions = set()  # 使用集合來管理訂閱
        self._conversations = set()
        self._connect()
        self._lock = threading.Lock()  # 添加線程鎖
        
        # 設置健康檢查定時器
        if self._health_check_item:
            self._health_check_timer = QTimer()
            self._health_check_timer.timeout.connect(self._check_connection)
            self._health_check_timer.start(int(self._check_interval * 1000))  # 轉換為毫秒
            print(f'[{timestamp()}] [INFO] [C] 啟動健康檢查定時器: 間隔={self._check_interval}秒')
            # 立即執行第一次健康檢查
            self._check_connection()
        
        self._reconnect_count = 0  # 添加重連計數器
        self._request_timeout = options.get('request_timeout', 5.0)  # 請求超時時間
        self._min_reconnect_interval = options.get('min_reconnect_interval', 1.0)  # 最小重連間隔
        self._last_reconnect_time = 0  # 上次重連時間

    def connect(self):
        """連接到 DDE 服務器"""
        try:
            if not self._is_connected():
                self._connect()
        except Exception as e:
            self.logger.error(f"連接失敗: {str(e)}")
            raise

    def disconnect(self, terminate_dde: bool = True):
        """斷開DDE連接
        
        Args:
            terminate_dde (bool): 是否要終止 DDE 連接。設為 False 時只會斷開當前會話，
                                不會影響其他使用相同 service.topic 的程式。
        """
        try:
            if not self._is_connected():
                self.logger.warning("[C] 未連接，無需斷開")
                return False
                
            # 取消所有訂閱
            for item in self.get_advised_items():
                self.logger.info(f"開始取消訂閱: {item}")
                self.unadvise(item)
                
            if terminate_dde:
                # 斷開連接
                if self._hConv:
                    self.logger.info(f"[C] 開始斷開 DDE 連接.: {self._service}.{self._topic}")
                    if not DDE.Disconnect(self._hConv):
                        error_code = DDE.GetLastError(self._idInst)
                        self.logger.error(f"[C] 斷開連接失敗，錯誤代碼: {error_code}")
                        return False
                        
                    self._hConv = None
                    self.logger.info(f"[C] 已成功斷開 DDE 連接: {self._service}.{self._topic}")
            else:
                    self.logger.info(f"[C] 不進行斷開 DDE 連接.")
                
            # 清理資源
            self._cleanup_resources()
            
            # 是否終止 DDE
            if terminate_dde:
                self.logger.debug("[C] 開始終止 DDE")
                if not DDE.Uninitialize(self._idInst):
                    error_code = DDE.GetLastError(self._idInst)
                    self.logger.error(f"[C] 終止 DDE 失敗，錯誤代碼: {error_code}")
                    return False
                    
                self._idInst = None
                self.logger.debug("[C] DDE 已終止")
            else:
                self.logger.debug("[C] 不進行終止 DDE")
                
            self.logger.info("[C] DDE 客戶端已完全清理")
            return True
            
        except Exception as e:
            self.logger.error(f"[C] 斷開連接時發生錯誤: {str(e)}")
            return False

    def is_connected(self) -> bool:
        """僅檢查基本連線狀態"""
        return self._connected and self._hConv is not None
        
    def _check_connection(self) -> bool:
        """執行健康檢查"""
        try:
            if not self.is_connected():
                return False
                
            print(f'[{timestamp()}] [DEBUG] [C] 執行健康檢查: {self._health_check_item}')
            result = self.request(self._health_check_item)
            
            # 更新最後檢查時間
            self._last_check_time = time.time()
            
            if result is not None:
                print(f'[{timestamp()}] [DEBUG] [C] 健康檢查成功: {result}')
                self._consecutive_failures = 0
                return True
                
            self._consecutive_failures += 1
            print(f'[{timestamp()}] [WARNING] [C] 健康檢查失敗 ({self._consecutive_failures}/{self._health_check_fail_threshold})')
            
            # 如果連續失敗次數達到閾值，觸發重連
            if self._consecutive_failures >= self._health_check_fail_threshold:
                print(f'[{timestamp()}] [WARNING] [C] 健康檢查失敗次數達到閾值，嘗試重新連接')
                self.disconnect()
                self.connect()
            
        except Exception as e:
            self._consecutive_failures += 1
            print(f'[{timestamp()}] [WARNING] [C] 健康檢查異常: {str(e)} ({self._consecutive_failures}/{self._health_check_fail_threshold})')
            
        return self._consecutive_failures < self._health_check_fail_threshold

    def __del__(self):
        """解構函數，確保資源被正確釋放"""
        print(f'[{timestamp()}] [DEBUG] [C] __del__ 開始釋放資源')
        try:
            if hasattr(self, 'idInst') and self.idInst:
                # 檢查 disconnect_on_exit 設定
                disconnect_on_exit = getattr(self, 'disconnect_on_exit', True)
                if disconnect_on_exit:
                    print(f'[{timestamp()}] [DEBUG] [C] __del__ 開始終止 DDE')
                    self._uninitialize()
                else:
                    print(f'[{timestamp()}] [DEBUG] [C] __del__ 不進行終止 DDE')
                    # 只清理資源，不取消初始化
                    self._cleanup_resources()
        except Exception as e:
            self.logger.error(f"解構時發生錯誤: {str(e)}")
    
    def _initialize(self) -> None:
        """初始化 DDE"""
        if not hasattr(self, '_idInst') or not self._idInst:
            print(f'[{timestamp()}] [DEBUG] [C] 開始初始化 DDE')
            self._idInst = DWORD(0)
            # 創建回調函數
            self._callback = DDEML_CALLBACK_TYPE(self._callback_handler)
            result = DDE.Initialize(
                byref(self._idInst),
                self._callback,
                APPCMD_CLIENTONLY | CBF_FAIL_ALLSVRXACTIONS,
                0
            )
            
            if result != DMLERR_NO_ERROR:
                error_msg = f"DDE 初始化失敗: 錯誤碼=0x{result:x}"
                print(f'[{timestamp()}] [ERROR] [C] {error_msg}')
                raise DDEClientError(error_msg)
            print(f'[{timestamp()}] [INFO] [C] DDE 初始化成功')

    def _uninitialize(self) -> None:
        """終止 DDE"""
        if hasattr(self, '_idInst') and self._idInst:
            print(f'[{timestamp()}] [DEBUG] [C] 開始終止 DDE')
            DDE.Uninitialize(self._idInst)
            self._idInst = None
            print(f'[{timestamp()}] [DEBUG] [C] DDE 已終止')

    def _connect(self):
        """連接到 DDE 服務器"""
        try:
            print(f'[{timestamp()}] [INFO] [C] 開始連接 DDE 服務器: service={self._service}, topic={self._topic}')
            self.logger.debug(f'開始連接 DDE 服務器: service={self._service}, topic={self._topic}')
            
            # 初始化 DDE
            self._initialize()
            
            # 連接到服務器
            self._connect_with_retry()
            
            # 添加到對話列表
            if self._hConv:
                self._conversations.add(self._hConv)
                print(f'[{timestamp()}] [INFO] [C] 成功連接到 DDE 服務器: {self._service}.{self._topic}')
                self.logger.debug(f'成功連接到 DDE 服務器: {self._service}.{self._topic}')
            
        except Exception as e:
            print(f'[{timestamp()}] [ERROR] [C] DDE 連接失敗: {str(e)}')
            self.logger.error(f'DDE 連接失敗: {str(e)}', exc_info=True)
            raise DDEClientError(f'連接失敗: {str(e)}')

    def _disconnect(self, terminate_dde: bool = True):
        """斷開與 DDE 服務器的連接"""
        try:
            if self._hConv:
                print(f'[{timestamp()}] [INFO] [C] 開始斷開 DDE 連接: {self._service}.{self._topic}')
                
                # 從對話列表中移除
                self._conversations.discard(self._hConv)
                self.logger.debug(f'已從對話列表中移除連接: {self._hConv}')
                
                # 取消所有訂閱
                subscription_count = len(self._subscriptions)
                if subscription_count > 0:
                    print(f'[{timestamp()}] [INFO] [C] 開始取消 {subscription_count} 個訂閱')
                    for item_name in list(self._subscriptions):
                        try:
                            self.unadvise(item_name)
                            self.logger.debug(f'已取消訂閱項目: {item_name}')
                        except Exception as e:
                            print(f'[{timestamp()}] [WARNING] [C] 取消訂閱項目 {item_name} 時發生錯誤: {str(e)}')
                
                # 斷開連接
                if terminate_dde:
                    DDE.Disconnect(self._hConv)
                    self._hConv = None
                    print(f'[{timestamp()}] [INFO] [C] 已成功斷開 DDE 連接: {self._service}.{self._topic}')
                else:
                    print(f'[{timestamp()}] [INFO] [C] 不進行斷開 DDE 連接: {self._service}.{self._topic}')
                
            if terminate_dde:
                self._uninitialize()
                print(f'[{timestamp()}] [INFO] [C] DDE 客戶端已完全清理')
            else:
                print(f'[{timestamp()}] [INFO] [C] 不進行清理 DDE 客戶端: {self._service}.{self._topic}')
            
        except Exception as e:
            print(f'[{timestamp()}] [ERROR] [C] 斷開連接失敗: {str(e)}')
            self.logger.error(f'斷開連接失敗: {str(e)}', exc_info=True)
            raise DDEClientError(f'斷開連接失敗: {str(e)}')

    def get_subscription_status(self) -> dict:
        """獲取訂閱狀態信息"""
        return {
            'active_subscriptions': list(self._subscriptions),
            'callback_count': len(self._callbacks),
            'is_connected': self._is_connected()
        }

    def _validate_subscription(self, item: str) -> bool:
        """驗證訂閱項目是否有效"""
        if not item or len(item) > 255:
            print(f'[{timestamp()}] [WARNING] [C] 無效的訂閱項目: {item}')
            return False
        if not self._is_connected():
            print(f'[{timestamp()}] [WARNING] [C] 無法驗證訂閱，DDE 未連接')
            return False
        return True

    def _validate_connection(self) -> bool:
        """驗證連接狀態"""
        if not self._is_connected():
            print(f'[{timestamp()}] [WARNING] [C] DDE 連接已斷開')
            return False
        return True

    def _handle_subscription_error(self, item: str, error: Exception) -> None:
        """處理訂閱錯誤"""
        error_msg = f"訂閱 {item} 失敗: {str(error)}"
        print(f'[{timestamp()}] [ERROR] [C] {error_msg}')
        self.logger.error(error_msg, exc_info=True)
        if item in self._subscriptions:
            self._subscriptions.remove(item)
            print(f'[{timestamp()}] [DEBUG] [C] 已從訂閱列表中移除: {item}')
        if item in self._callbacks:
            del self._callbacks[item]
            print(f'[{timestamp()}] [DEBUG] [C] 已從回調列表中移除: {item}')

    def advise(self, item: str, callback: Callable) -> bool:
        """訂閱數據更新"""
        try:
            print(f'[{timestamp()}] [INFO] [C] 開始訂閱: {self._service}.{self._topic}.{item}')
            
            if not self._validate_subscription(item):
                return False
                
            if not self._validate_connection():
                return False

            print(f'[{timestamp()}] [DEBUG] [C] 開始訂閱處理: {self._topic}.{item}')
            
            # 添加短暫延遲，確保連接完全建立
            time.sleep(0.1)
            
            if not self._is_connected():
                print(f'[{timestamp()}] [DEBUG] [C] 檢測到連接斷開，嘗試重連')
                self._connect_with_retry()
                
            # 再次檢查連接
            if not self._is_connected():
                print(f'[{timestamp()}] [DEBUG] [C] 重連後仍未連接')
                return False
            
            # 創建項目句柄
            hsz_item = DDE.CreateStringHandle(
                self._idInst,
                item,
                CP_WINUNICODE
            )
            
            if not hsz_item:
                print(f'[{timestamp()}] [ERROR] [C] 創建項目句柄失敗: {item}')
                return False
                
            try:
                # 開始訂閱
                print(f'[{timestamp()}] [DEBUG] [C] 發送訂閱請求: _hConv={self._hConv}, hsz_item={hsz_item}')
                result = DDE.ClientTransaction(
                    None, 0,
                    self._hConv, hsz_item,
                    CF_TEXT, XTYP_ADVSTART,
                    TIMEOUT_ASYNC, None
                )
                
                if result:
                    self._callbacks[item] = callback
                    self._subscriptions.add(item)
                    print(f'[{timestamp()}] [INFO] [C] 成功訂閱: {self._service}.{self._topic}.{item}')
                    print(f'[{timestamp()}] [DEBUG] [C] 當前訂閱數: {len(self._subscriptions)}')
                    return True
                else:
                    error_code = DDE.GetLastError(self._idInst)
                    print(f'[{timestamp()}] [ERROR] [C] 訂閱失敗: {self._service}.{self._topic}.{item}, 錯誤碼: 0x{error_code:x}')
                    return False
                    
            finally:
                DDE.FreeStringHandle(self._idInst, hsz_item)
                
        except Exception as e:
            self._handle_subscription_error(item, e)
            return False

    def request(self, item: str, timeout: Optional[int] = None) -> Optional[str]:
        """同步請求數據
        
        Args:
            item: 要請求的項目名稱
            timeout: 超時時間（毫秒），None 則使用默認配置
            
        Returns:
            str | None: 請求到的數據，失敗返回 None
            
        Raises:
            DDEClientError: 當發生連接錯誤時
        """
        with self._lock:  # 使用線程鎖保護 DDE 操作
            try:
                self.logger.debug(f'[C] request - 開始請求數據: {item}')
                self.logger.debug(f'[C] request - _idInst: {self._idInst}, _hConv: {self._hConv}')

                if not self._is_connected():
                    self.logger.debug(f'[C] request - 檢測到未連接，嘗試重連')
                    self._connect_with_retry()
                    
                    if not self._is_connected():
                        raise DDEClientError("未連接到服務器")

                # 創建項目句柄
                hsz_item = DDE.CreateStringHandle(
                    self._idInst,
                    item,
                    CP_WINUNICODE
                )
                
                if not hsz_item:
                    raise DDEClientError("創建項目句柄失敗")
                
                try:
                    # 發送請求
                    hData = DDE.ClientTransaction(
                        None, 0,
                        self._hConv,
                        hsz_item,
                        CF_TEXT,
                        XTYP_REQUEST,
                        timeout or self.config.timeout,
                        None
                    )
                    
                    if not hData:
                        error_code = DDE.GetLastError(self._idInst)
                        raise DDEClientError(
                            f"請求失敗: {item}",
                            self._idInst,
                            error_code
                        )
                        
                    try:
                        # 獲取數據大小
                        data_size = DWORD()
                        data_ptr = DDE.AccessData(hData, byref(data_size))
                        
                        if not data_ptr:
                            error_code = DDE.GetLastError(self._idInst)
                            self.logger.error(f'[C] request - 訪問數據失敗，錯誤碼: 0x{error_code:x}')
                            return None
                            
                        try:
                            # 讀取數據
                            data = string_at(data_ptr, data_size.value)
                            self.logger.debug(f'[C] request - 讀取到原始數據: {data}')
                            
                            # 解碼數據
                            result = self._decode_data(data)
                            self.logger.debug(f'[C] request - 解碼後的數據: {result}')
                            return result
                            
                        finally:
                            self.logger.debug(f'[C] request - 釋放數據訪問')
                            DDE.UnaccessData(hData)
                            
                    finally:
                        self.logger.debug(f'[C] request - 釋放數據句柄')
                        DDE.FreeDataHandle(hData)
                        
                finally:
                    self.logger.debug(f'[C] request - 釋放項目句柄')
                    DDE.FreeStringHandle(self._idInst, hsz_item)
                    
            except Exception as e:
                self.logger.error(f'[C] request - 發生錯誤: {str(e)}')
                raise DDEClientError(f"請求數據失敗: {str(e)}")

    def request_async(self, item: str, callback: Callable[[str, Optional[str]], None],
                     timeout: Optional[int] = None) -> None:
        """非阻塞請求數據
        
        Args:
            item: 要請求的項目名稱
            callback: 回調函數，接收兩個參數：item_name 和 value（可能為 None）
            timeout: 超時時間（毫秒），None 則使用默認配置
        """
        def _request_thread():
            try:
                # 在新線程中創建新的客戶端
                thread_client = DDEClient(self._service, self._topic)
                try:
                    result = thread_client.request(item, timeout)
                    callback(item, result)
                finally:
                    thread_client.disconnect()
            except Exception as e:
                self.logger.error(f"異步請求失敗: {str(e)}")
                callback(item, None)

        thread = threading.Thread(
            target=_request_thread,
            name=f"DDE-Request-{item}-{int(time.time())}"
        )
        thread.daemon = True
        thread.start()

    async def request_async_await(self, item: str, timeout: Optional[int] = None) -> Optional[str]:
        """異步請求數據（支持 await）"""
        try:
            # 設置默認超時
            if timeout is None:
                timeout = self.config.timeout
                
            # 創建 Future 對象
            loop = asyncio.get_event_loop()
            future = loop.create_future()
            
            def callback(item: str, value: Optional[str]):
                if not future.done():
                    future.set_result(value)
            
            # 發送請求
            self.request_async(item, callback)
            
            # 等待結果，帶超時
            try:
                return await asyncio.wait_for(future, timeout=timeout/1000.0)
            except asyncio.TimeoutError:
                raise DDEClientError("請求超時")
                
        except Exception as e:
            raise DDEClientError(f"異步請求失敗: {str(e)}")

    def unadvise(self, item: str) -> bool:
        """取消訂閱"""
        try:
            self.logger.info(f'開始取消訂閱: {self._topic}.{item}')
            
            if not self._is_connected():
                self.logger.debug(f'[C] unadvise - 未連接到服務器')
                return False
                
            if item not in self._subscriptions:
                self.logger.debug(f'[C] unadvise - 項目未訂閱: {item}')
                return False
                
            # 創建項目句柄
            hsz_item = DDE.CreateStringHandle(
                self._idInst,
                item,
                CP_WINUNICODE
            )
            
            if not hsz_item:
                self.logger.debug(f'[C] unadvise - 創建項目句柄失敗')
                return False
                
            try:
                # 發送取消訂閱請求
                self.logger.debug(f'[C] unadvise - 發送取消訂閱請求: _hConv={self._hConv}, hsz_item={hsz_item}')
                result = DDE.ClientTransaction(
                    None, 0,
                    self._hConv, hsz_item,
                    CF_TEXT, XTYP_ADVSTOP,
                    TIMEOUT_ASYNC, None
                )
                
                if result:
                    self._subscriptions.discard(item)
                    if item in self._callbacks:
                        del self._callbacks[item]
                    print(f"[{timestamp()}] [INFO] [C] 成功取消訂閱: {self._topic}.{item}")
                    return True
                else:
                    error_code = DDE.GetLastError(self._idInst)
                    print(f'[{timestamp()}] [ERROR] [C] 取消訂閱失敗: {self._topic}.{item}, 錯誤碼: 0x{error_code:x}')
                    return False
                    
            finally:
                DDE.FreeStringHandle(self._idInst, hsz_item)
                
        except Exception as e:
            self.logger.error(f'[C] unadvise - 取消訂閱時發生錯誤: {str(e)}')
            return False

    def _connect_with_retry(self) -> None:
        """嘗試建立連接，支持重試"""
        for attempt in range(self.config.max_retries):
            try:
                if self._is_connected():
                    print(f'[{timestamp()}] [DEBUG] [C] _connect_with_retry - 已經連接')
                    return
                    
                print(f'[{timestamp()}] [DEBUG] [C] _connect_with_retry - 嘗試連接 (第{attempt + 1}次)')
                
                # 如果已有連接，先完全斷開
                if hasattr(self, '_hConv') and self._hConv:
                    print(f'[{timestamp()}] [DEBUG] [C] _connect_with_retry - 斷開舊連接: _hConv={self._hConv}')
                    DDE.Disconnect(self._hConv)
                    self._hConv = None
                    self._connected = False
                    
                # 如果已初始化，先清理
                if hasattr(self, '_idInst') and self._idInst:
                    DDE.Uninitialize(self._idInst)
                    self._idInst = None
                
                # 重新初始化 DDE
                self._idInst = DWORD(0)
                self._callback = DDEML_CALLBACK_TYPE(self._callback_handler)
                
                result = DDE.Initialize(
                    byref(self._idInst), 
                    self._callback, 
                    APPCMD_CLIENTONLY | APPCLASS_STANDARD,
                    0
                )
                
                if result != DMLERR_NO_ERROR:
                    error_code = DDE.GetLastError(self._idInst)
                    print(f'[{timestamp()}] [ERROR] [C] _connect_with_retry - DDE 初始化失敗: 0x{error_code:x}')
                    time.sleep(0.5)  # 添加延遲
                    continue
                
                # 建立連接
                hsz_service = DDE.CreateStringHandle(
                    self._idInst,
                    self._service,
                    CP_WINUNICODE
                )
                hsz_topic = DDE.CreateStringHandle(
                    self._idInst,
                    self._topic,
                    CP_WINUNICODE
                )
                
                print(f'[{timestamp()}] [DEBUG] [C] _connect_with_retry - 嘗試連接: {self._service}.{self._topic}')
                self._hConv = DDE.Connect(
                    self._idInst,
                    hsz_service,
                    hsz_topic,
                    None
                )
                
                DDE.FreeStringHandle(self._idInst, hsz_service)
                DDE.FreeStringHandle(self._idInst, hsz_topic)
                
                if self._hConv:
                    print(f'[{timestamp()}] [DEBUG] [C] _connect_with_retry - 連接成功: _hConv={self._hConv}')
                    self._connected = True
                    return
                
                error_code = DDE.GetLastError(self._idInst)
                print(f'[{timestamp()}] [ERROR] [C] _connect_with_retry - 連接失敗: 0x{error_code:x}')
                time.sleep(0.5)  # 添加延遲
                
            except Exception as e:
                print(f'[{timestamp()}] [ERROR] [C] _connect_with_retry - 發生錯誤: {str(e)}')
                time.sleep(0.5)  # 添加延遲
                
        raise DDEClientError("無法建立連接")

    def _is_connected(self) -> bool:
        """檢查連接狀態"""
        try:
            # 基本檢查
            if not hasattr(self, '_hConv') or not self._hConv:
                print(f'[{timestamp()}] [DEBUG] [C] 無效的對話句柄: _hConv={getattr(self, "_hConv", None)}')
                self._connected = False
                return False
                
            if not hasattr(self, '_idInst') or not self._idInst:
                print(f'[{timestamp()}] [DEBUG] [C] 無效的實例ID')
                self._connected = False
                return False
                
            # 只檢查基本的連接句柄有效性
            is_connected = bool(self._hConv and self._idInst)
            self._connected = is_connected
            print(f'[{timestamp()}] [DEBUG] [C] 連接狀態: {is_connected}')
            return is_connected
            
        except Exception as e:
            error_msg = f'檢查連接狀態時發生錯誤: {str(e)}'
            print(f'[{timestamp()}] [ERROR] [C] {error_msg}')
            self.logger.error(error_msg, exc_info=True)
            self._connected = False
            return False

    def _callback_handler(self, uType: int, uFmt: int, hConv: int, 
                         hsz1: int, hsz2: int, hData: int, 
                         dwData1: int, dwData2: int) -> int:
        """DDE 回調處理函數"""
        try:
            hsz1_string = DDE.query_string(self._idInst, hsz1)
        except Exception as e:
            print(f'[{timestamp()}] [ERROR] [C] query_string 失敗: hsz1={hsz1}, 錯誤: {str(e)}')
            hsz1_string = "unknown"

        try:
            hsz2_string = DDE.query_string(self._idInst, hsz2)
        except Exception as e:
            print(f'[{timestamp()}] [ERROR] [C] query_string 失敗: hsz2={hsz2}, 錯誤: {str(e)}')
            hsz2_string = "unknown"
        
        self.logger.debug(f'[INFO] [C] 收到 DDE 回調: 類型=0x{uType:x}, 格式={uFmt}, 對話句柄={hConv}')
        self.logger.debug(f'[INFO] [C] 回調詳細信息: hsz1={hsz1}({hsz1_string}), hsz2={hsz2}({hsz2_string}), hData={hData}, dwData1={dwData1}, dwData2={dwData2}')

        try:
            if uType == XTYP_ADVDATA:
                self.logger.debug(f'[INFO] [C] 處理數據更新通知')

                if not hData:
                    return DDE_FNOTPROCESSED
                    
                try:
                    data_size = DWORD()
                    data_ptr = DDE.AccessData(hData, byref(data_size))
                    if not data_ptr:
                        return DDE_FNOTPROCESSED
                        
                    try:
                        # 讀取數據
                        value = string_at(data_ptr, data_size.value).decode('cp950').rstrip('\0')
                        topic_name = DDE.query_string(self._idInst, hsz1)
                        item_name = DDE.query_string(self._idInst, hsz2)
                        self.logger.debug(f'[INFO] [C] 收到數據更新: {topic_name}.{item_name} = {value}')
                        
                        # 如果有註冊回調函數，則調用
                        if item_name in self._callbacks:
                            #self._callbacks[item_name](value, item_name)
                            self._callbacks[item_name](item_name, value)
                            
                        return DDE_FACK
                        
                    finally:
                        DDE.UnaccessData(hData)
                        
                except Exception as e:
                    print(f'[{timestamp()}] [ERROR] [C] 處理數據更新失敗: {str(e)}')
                    return DDE_FNOTPROCESSED
                    
            elif uType == XTYP_DISCONNECT:
                self.logger.info(f"[C] 收到斷開連接通知")
                self._handle_disconnect("DDE服務器主動斷開連接")
                
            return DDE_FNOTPROCESSED
            
        except Exception as e:
            self.logger.error(f"處理回調時發生錯誤: {str(e)}")
            return DDE_FNOTPROCESSED

    def _send_request(self, item: str) -> Optional[str]:
        """請求數據"""
        try:
            if not self._hConv:
                raise DDEClientError("未連接到服務器")
                
            self.logger.debug(f"[REQUEST] 準備請求數據: {item}")
            
            # 追蹤資源
            hszItem = self._get_string_handle(item)
            self._track_resource(
                hszItem,
                lambda h: DDE.FreeStringHandle(self._idInst, h),
                f"項目句柄: {item}"
            )
            
            hDdeData = DDE.ClientTransaction(
                None, 0,
                self._hConv, hszItem,
                CF_TEXT, XTYP_REQUEST,
                self.config.timeout, None,
                None
            )
            
            if not hDdeData:
                error_code = DDE.GetLastError(self._idInst)
                raise DDEClientError(
                    f"請求失敗: {item}",
                    self._idInst,
                    error_code
                )
            
            # 追蹤數據句柄
            self._track_resource(
                hDdeData,
                DDE.FreeDataHandle,
                f"數據句柄: {item}"
            )
            
            try:
                data = DDE.get_data(hDdeData)
                if data:
                    value = self._decode_data(data)
                    self.logger.debug(f"[REQUEST] 數據請求成功: {item} = {value!r}")
                    return value
                self.logger.warning(f"[REQUEST] 未收到數據: {item}")
                return None
                
            finally:
                # 資源會在 _cleanup_resources 中被清理
                pass
            
        except DDEError:
            raise
        except Exception as e:
            raise DDEClientError(f"請求時發生錯誤: {str(e)}")

    def _start_advise(self, item: str, callback: Callable) -> bool:
        """訂閱數據更新"""
        try:
            if not self._hConv:
                self.logger.error("[ADVISE] 未連接到服務器")
                return False
                
            # 創建項目句柄
            hszItem = self._get_string_handle(item)
            if not hszItem:
                self.logger.error(f"[ADVISE] 創建項目句柄失敗: {item}")
                return False
                
            # 註冊回調
            if callback:
                item_key = item.encode('utf-8')
                self._callbacks[item_key] = callback
                self.logger.debug(f"[ADVISE] 註冊回調函數: {item}")
            
            # 開始訂閱
            result = DDE.ClientTransaction(
                None, 0,
                self._hConv, hszItem,
                CF_TEXT, XTYP_ADVSTART | XTYPF_ACKREQ,
                TIMEOUT_ASYNC, None,
                None
            )
            
            if not result:
                error_code = DDE.GetLastError(self._idInst)
                self.logger.error(f"[ADVISE] 訂閱失敗，錯誤碼: 0x{error_code:x}")
                # 清理回調
                item_key = item.encode('utf-8')
                if item_key in self._callbacks:
                    del self._callbacks[item_key]
                return False
                
            self._subscriptions.add(item)  # 記錄訂閱項目
            self.logger.info(f"[ADVISE] 成功訂閱項目: {item}")
            return True
            
        except Exception as e:
            self.logger.error(f"[ADVISE] 訂閱失敗: {str(e)}")
            return False

    def _stop_advise(self, item: str) -> bool:
        """取消訂閱數據更新"""
        try:
            if not self._hConv:
                self.logger.error("[UNADVISE] 未連接到服務器")
                return False
            
            if item not in self._subscriptions:
                self.logger.warning(f"[UNADVISE] 項目未訂閱: {item}")
                return True
            
            # 創建項目句柄
            hszItem = self._get_string_handle(item)
            if not hszItem:
                self.logger.error(f"[UNADVISE] 創建項目句柄失敗: {item}")
                return False
            
            try:
                # 停止訂閱
                result = DDE.ClientTransaction(
                    None, 0,
                    self._hConv, hszItem,
                    CF_TEXT, XTYP_ADVSTOP,
                    TIMEOUT_ASYNC, None,
                    None
                )
                
                if not result:
                    error_code = DDE.GetLastError(self._idInst)
                    self.logger.error(f"[UNADVISE] 取消訂閱失敗，錯誤碼: 0x{error_code:x}")
                    return False
                
                # 清理回調
                item_key = item.encode('utf-8')
                if item_key in self._callbacks:
                    del self._callbacks[item_key]
                
                self._subscriptions.remove(item)
                self.logger.info(f"[UNADVISE] 成功取消訂閱: {item}")
                return True
                
            finally:
                if hszItem:
                    DDE.FreeStringHandle(self._idInst, hszItem)
            
        except Exception as e:
            self.logger.error(f"[UNADVISE] 取消訂閱失敗: {str(e)}")
            return False

    def _get_string_handle(self, item: str) -> HSZ:
        """獲取或創建字符串句柄"""
        if item not in self._string_handles:
            self._string_handles[item] = DDE.CreateStringHandle(self._idInst, item, CP_WINUNICODE)
            self.logger.debug(f"創建字符串句柄: {item}")
        return self._string_handles[item]

    def clear_callbacks(self):
        """清除所有回調"""
        # 先取消所有訂閱
        for item in list(self._subscriptions):
            self.unadvise(item)
        # 清除回調字典
        self._callbacks.clear()
        # 確清空訂閱集合
        self._subscriptions.clear()

    def printDDE(self, value: str, item: Optional[str] = None):
        """默認的回調函數"""
        self.logger.info(f"{item}:{value}")

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器退出時調用"""
        print(f'[{timestamp()}] [DEBUG] [C] __exit__ 開始釋放資源')
        try:
            # 檢查 disconnect_on_exit 設定
            disconnect_on_exit = getattr(self, 'disconnect_on_exit', True)
            if disconnect_on_exit:
                print(f'[{timestamp()}] [DEBUG] [C] __exit__ 開始終止 DDE')
                self._uninitialize()
            else:
                print(f'[{timestamp()}] [DEBUG] [C] __exit__ 不進行終止 DDE')
                # 只清理資源，不取消初始化
                self._cleanup_resources()
        except Exception as e:
            print(f'[{timestamp()}] [ERROR] [C] __exit__ 釋放資源時發生錯誤: {str(e)}')

    def xxx__del__(self):
        """確保資源被清理"""
        try:
            self._cleanup_resources()
        except Exception as e:
            # 這裡不使用 logger,因為在對象銷毀時 logger 可能已不可用
            self.logger.error(f"清理資源時發生錯誤: {str(e)}")

    def poke(self, item_name: str, value: str) -> bool:
        """向服務器發送 POKE 請求"""
        try:
            if not self._hConv:
                self.logger.error("[POKE] 未連接到服務器")
                return False
            
            # 創建項目句柄
            hsz_item = DDE.CreateStringHandle(
                self._idInst,
                item_name,
                CP_WINUNICODE
            )
            if not hsz_item:
                raise DDEClientError("創建項目句柄失敗", self._idInst)
            
            try:
                # 創建數據
                value_bytes = str(value).encode('cp950') + b'\0'
                
                self.logger.debug(f"[POKE] 準備發送數據: {item_name} = {value!r}")
                
                # 發送 POKE 請求
                result = DDE.ClientTransaction(
                    value_bytes,         # pData
                    len(value_bytes),    # cbData
                    self._hConv,         # hConv
                    hsz_item,            # hsz1
                    CF_TEXT,             # wFmt
                    XTYP_POKE,          # wType
                    DDE_TIMEOUT,         # dwTimeout
                    None               # pdwResult
                )
                
                if not result:
                    error_code = DDE.GetLastError(self._idInst)
                    self.logger.error(f"[POKE] 請求失敗，錯誤碼: 0x{error_code:x}")
                    return False
                    
                self.logger.info(f"[POKE] 數據發送成功: {item_name} = {value!r}")
                return True
                
            finally:
                if hsz_item:
                    DDE.FreeStringHandle(self._idInst, hsz_item)
                
        except Exception as e:
            self.logger.error(f"[POKE] 發送數據失敗: {str(e)}")
            return False

    def execute(self, command: str) -> bool:
        """向服務器發送 EXECUTE 命令"""
        try:
            if not self._hConv:
                self.logger.error("[EXECUTE] 未連接到服務器")
                return False
            
            # 創建命令數據
            try:
                # 移除尾部空白並確保命令不為空
                command = command.rstrip()
                if not command:
                    self.logger.error("[EXECUTE] 命令不能為空")
                    return False
                
                # 使用 CP950 編碼
                command_bytes = command.encode('cp950')
                # 確保以 null 結尾
                if not command_bytes.endswith(b'\x00'):
                    command_bytes += b'\x00'
                
                self.logger.debug(f"[EXECUTE] 準備發送命令: {command!r}")
                
            except Exception as e:
                self.logger.error(f"[EXECUTE] 準備命令失敗: {str(e)}")
                return False
            
            # 發送 EXECUTE 請求
            result = DDE.ClientTransaction(
                command_bytes,       # pData
                len(command_bytes),  # cbData
                self._hConv,         # hConv
                None,               # hsz1 - 必須為 NULL
                CF_TEXT,            # wFmt
                XTYP_EXECUTE,       # wType
                DDE_TIMEOUT,        # dwTimeout
                None              # pdwResult
            )
            
            if not result:
                error_code = DDE.GetLastError(self._idInst)
                if error_code == DMLERR_NOTPROCESSED:
                    self.logger.error("[EXECUTE] 服務器拒絕執行命令")
                elif error_code == DMLERR_BUSY:
                    self.logger.error("[EXECUTE] 服務器正忙")
                else:
                    self.logger.error(f"[EXECUTE] 執行失敗，錯誤碼: 0x{error_code:x}")
                return False
            
            self.logger.info(f"[EXECUTE] 命令發送成功: {command!r}")
            return True
            
        except Exception as e:
            self.logger.error(f"[EXECUTE] 執行命令失敗: {str(e)}")
            return False

    def _check_connection(self) -> bool:
        """執行健康檢查"""
        try:
            if not self._connected or not self._hConv:
                return False
                
            print(f'[{timestamp()}] [DEBUG] [C] 執行健康檢查: {self._health_check_item}')
            result = self.request(self._health_check_item)
            
            # 更新最後檢查時間
            self._last_check_time = time.time()
            
            if result is not None:
                print(f'[{timestamp()}] [DEBUG] [C] 健康檢查成功: {result}')
                self._consecutive_failures = 0
                return True
                
            self._consecutive_failures += 1
            print(f'[{timestamp()}] [WARNING] [C] 健康檢查失敗 ({self._consecutive_failures}/{self._health_check_fail_threshold})')
            
            # 如果連續失敗次數達到閾值，觸發重連
            if self._consecutive_failures >= self._health_check_fail_threshold:
                print(f'[{timestamp()}] [WARNING] [C] 健康檢查失敗次數達到閾值，嘗試重新連接')
                self.disconnect()
                self.connect()
            
        except Exception as e:
            self._consecutive_failures += 1
            print(f'[{timestamp()}] [WARNING] [C] 健康檢查異常: {str(e)} ({self._consecutive_failures}/{self._health_check_fail_threshold})')
            
        return self._consecutive_failures < self._health_check_fail_threshold
        
    def _handle_disconnect(self, reason: str):
        """處理斷線事件"""
        if not self._connected:  # 避免重複處理
            return
            
        print(f'[{timestamp()}] [WARNING] [C] 處理斷線事件: {reason}')
        self._connected = False
        self._hConv = None
        
        if self._auto_reconnect:
            self._reconnect()
            
    def _reconnect(self):
        """執行重連程序"""
        print(f'[{timestamp()}] [INFO] [C] 開始重連程序')
        reconnect_count = 0
        
        while reconnect_count < self._max_reconnect_attempts:
            try:
                print(f'[{timestamp()}] [INFO] [C] 嘗試重新連接 (第{reconnect_count + 1}次)')
                self.connect()
                
                # 重連成功後重置健康檢查狀態
                self._consecutive_failures = 0
                self._last_check_time = time.time()
                
                # 重連後確認連線狀態
                if self._connected:
                    print(f'[{timestamp()}] [INFO] [C] 重新連接成功')
                    if self._reconnect_callback:
                        self._reconnect_callback(True, "重新連接成功")
                        
                    # 重新訂閱所有項目
                    for item_name, callback in self._callbacks.items():
                        print(f'[{timestamp()}] [INFO] [C] 重新訂閱: {item_name}')
                        self.advise(item_name, callback)
                    return True
                    
            except Exception as e:
                print(f'[{timestamp()}] [ERROR] [C] 重新連接失敗: {str(e)}')
                
            reconnect_count += 1
            if reconnect_count < self._max_reconnect_attempts:
                time.sleep(self._reconnect_interval)
                
        # 重連失敗
        if self._reconnect_callback:
            self._reconnect_callback(False, f"重連失敗，已嘗試 {self._max_reconnect_attempts} 次")
        print(f'[{timestamp()}] [ERROR] [C] 重連失敗，已達最大嘗試次數')
        return False

    def ensure_connected(self) -> bool:
        """確保連接有效，如果斷開則嘗試重連"""
        if not self._check_connection():
            try:
                self._connect_with_retry()
                return True
            except DDEClientError:
                return False
        return True

    def _get_execute_command(self, hData: HDDEDATA) -> Optional[str]:
        """獲取執行命令"""
        try:
            if not hData:
                return None
                
            # 獲取數據大小
            size = DDE.GetData(hData, None, 0, 0)
            if not size:
                self.logger.debug(f'[C] _get_execute_command - 無法獲取數據大小')
                return None
                
            # 創建緩衝區並讀取數據
            buffer = create_string_buffer(size)
            result = DDE.GetData(hData, buffer, size, 0)
            if result != size:
                self.logger.debug(f'[C] _get_execute_command - 讀取數據失敗: 預期 {size} 字節，實際讀取 {result} 字節')
                return None
                
            # 轉換為字符串，去掉結尾的 null
            command = buffer.raw.decode('utf-8').rstrip('\0')
            self.logger.debug(f'[C] _get_execute_command - 成功讀取命令: {command}')
            return command
                
        except Exception as e:
            self.logger.error(f'[C] _get_execute_command - 錯誤: {str(e)}')
            return None

    def get_advised_items(self) -> set:
        """獲取當前訂閱的項目列表"""
        return self._subscriptions.copy()

    def get_item_status(self, item: str) -> dict:
        """獲取指定項目的狀態"""
        return {
            'subscribed': item in self._subscriptions,
            'has_callback': item in self._callbacks,
            'last_update': getattr(self, f'_last_update_{item}', None)
        }

    def is_subscribed(self, item: str) -> bool:
        """檢查項目是否已訂閱"""
        return item in self._subscriptions

    def reconnect(self) -> bool:
        """重新連接"""
        # 避免過於頻繁的重連
        now = time.time()
        if now - self._last_reconnect_time < self._min_reconnect_interval:
            print(f'[{timestamp()}] [INFO] [C] 重連過於頻繁，等待間隔')
            return False
            
        self._last_reconnect_time = now
        if not self._auto_reconnect or not self._hConv:
            return False
            
        while self._reconnect_count < self._max_reconnect_attempts:
            try:
                print(f'[{timestamp()}] [INFO] [C] 嘗試重新連接 (第{self._reconnect_count + 1}次)')
                new_hConv = DDE.Reconnect(self._hConv)
                
                if new_hConv:
                    self._hConv = new_hConv
                    self._connected = True
                    self._reconnect_count = 0  # 重置計數
                    
                    if self._reconnect_callback:
                        self._reconnect_callback(True, "重新連接成功")
                        
                    print(f'[{timestamp()}] [INFO] [C] DDE 重新連接成功')
                    return True
                    
            except Exception as e:
                print(f'[{timestamp()}] [ERROR] [C] 重新連接失敗: {str(e)}')
                
            self._reconnect_count += 1
            time.sleep(self._reconnect_interval)
            
        # 重連失敗
        if self._reconnect_callback:
            self._reconnect_callback(False, f"重連失敗，已嘗試 {self._max_reconnect_attempts} 次")
            
        print(f'[{timestamp()}] [ERROR] [C] 重連失敗，已達最大嘗試次數')
        return False

    def _handle_disconnect(self, reason: str):
        """處理斷線事件"""
        if not self._connected:  # 避免重複處理
            return
            
        print(f'[{timestamp()}] [WARNING] [C] 處理斷線事件:. {reason}')
        self._connected = False
        self._hConv = None
        
        # 如果啟用了自動重連，就嘗試重新連接
        if self._auto_reconnect:
            self._reconnect()

    def update_health_check_settings(self, item=None, interval=None, threshold=None):
        """更新健康檢查設定"""
        if item is not None:
            self._health_check_item = item
            print(f'[{timestamp()}] [INFO] [C] 更新健康檢查項目: {item}')
            
        if interval is not None and interval > 0:
            self._check_interval = interval
            if hasattr(self, '_health_check_timer'):
                self._health_check_timer.setInterval(int(interval * 1000))
            print(f'[{timestamp()}] [INFO] [C] 更新健康檢查間隔: {interval}秒')
            
        if threshold is not None and threshold > 0:
            self._health_check_fail_threshold = threshold
            print(f'[{timestamp()}] [INFO] [C] 更新健康檢查失敗閾值: {threshold}次')
            
        # 立即執行一次健康檢查
        self._check_connection()

    def _reconnect_conversation(self):
        """重新連接特定的對話"""
        try:
            # 先嘗試使用 DdeReconnect（如果服務還存在）
            if self._hConv:
                try:
                    new_hConv = DDE.Reconnect(self._hConv)
                    if new_hConv:
                        self._hConv = new_hConv
                        self._connected = True
                        print(f'[{timestamp()}] [INFO] [C] 快速重連成功: {self._conversation_id}')
                        return True
                except:
                    pass  # 如果快速重連失敗，繼續完整重連流程
            
            # 如果快速重連失敗，進行完整重連
            print(f'[{timestamp()}] [INFO] [C] 開始完整重連: {self._conversation_id}')
            
            # 只清理當前對話的資源
            if self._hConv:
                DDE.Disconnect(self._hConv)
                self._hConv = None
            
            # 重新建立此特定對話的連線
            hsz_service = DDE.CreateStringHandle(self._idInst, self._service, CP_WINUNICODE)
            hsz_topic = DDE.CreateStringHandle(self._idInst, self._topic, CP_WINUNICODE)
            
            try:
                self._hConv = DDE.Connect(self._idInst, hsz_service, hsz_topic, None)
                if self._hConv:
                    self._connected = True
                    # 重新訂閱此對話的項目
                    if self._subscriptions:
                        for item in list(self._subscriptions):
                            if item in self._callbacks:
                                self.advise(item, self._callbacks[item])
                    return True
            finally:
                DDE.FreeStringHandle(self._idInst, hsz_service)
                DDE.FreeStringHandle(self._idInst, hsz_topic)
            
            return False
            
        except Exception as e:
            print(f'[{timestamp()}] [ERROR] [C] 重連失敗 {self._conversation_id}: {str(e)}')
            return False

    def update_reconnect_settings(self, 
                                auto_reconnect: bool = None,
                                max_attempts: int = None,
                                interval: float = None,
                                min_interval: float = None):
        """更新自動重連設定
        
        Args:
            auto_reconnect: 是否啟用自動重連
            max_attempts: 最大重試次數
            interval: 重試間隔（秒）
            min_interval: 最小重連間隔（秒）
        """
        if auto_reconnect is not None:
            self._auto_reconnect = auto_reconnect
            
        if max_attempts is not None:
            if not (1 <= max_attempts <= 10):
                raise ValueError("max_attempts 必須在 1-10 之間")
            self._max_reconnect_attempts = max_attempts
            
        if interval is not None:
            if not (0.1 <= interval <= 10.0):
                raise ValueError("interval 必須在 0.1-10.0 之間")
            self._reconnect_interval = interval
            
        if min_interval is not None:
            if not (0.1 <= min_interval <= 30.0):
                raise ValueError("min_interval 必須在 0.1-30.0 之間")
            self._min_reconnect_interval = min_interval
            
        print(f'[{timestamp()}] [INFO] [C] 已更新重連設定: '
              f'啟用={self._auto_reconnect}, '
              f'最大次數={self._max_reconnect_attempts}, '
              f'間隔={self._reconnect_interval}秒, '
              f'最小間隔={self._min_reconnect_interval}秒')

class DDEServer(DDEBase):
    """DDE 服務器類"""
    def __init__(self, service_name: str):
        """初始化 DDE 服務器"""
        super().__init__()
        self._service_name = service_name
        self._topics = {}  # {topic_name: {"handle": handle, "items": {item_name: {"handle": handle, "value": value}}}}
        self._hsz_service = None
        self._idInst = None
        self._initialized = False  # 添加這一行
        self.logger = logging.getLogger(__name__)
        self._conversations = set()  # 存儲活動的對話句柄
        self._callback = DDEML_CALLBACK_TYPE(self._callback_handler)
        self._initialize()
        self._register_service()

    def _initialize(self) -> None:
        """初始化 DDE 服務器"""
        self._idInst = DWORD(0)
        result = DDE.Initialize(
            byref(self._idInst),
            self._callback,
            APPCLASS_STANDARD,  # 修改：使用標準服務器類型
            0
        )
        
        if result != DMLERR_NO_ERROR:
            raise DDEServerError(f"DDE 初始化失敗", self._idInst, result)

    def _register_service(self) -> None:
        """註冊 DDE 服務"""
        # 創建服務名稱句柄
        self._hsz_service = DDE.CreateStringHandle(
            self._idInst,
            self._service_name,
            CP_WINUNICODE
        )
        
        if not self._hsz_service:
            raise DDEServerError("創建服務名稱句柄失敗")
            
        # 註冊服務
        result = DDE.NameService(
            self._idInst,
            self._hsz_service,
            0,
            DNS_REGISTER
        )
        
        if not result:
            error_code = DDE.GetLastError(self._idInst)
            raise DDEServerError(
                f"註冊服務失敗: {self._service_name}",
                self._idInst,
                error_code
            )

    def register_topic(self, topic_name: str) -> bool:
        """註冊主題"""
        try:
            if topic_name in self._topics:
                self.logger.info(f"主題已存在: {topic_name}")
                return True
            
            # 創建主題句柄
            hsz_topic = DDE.CreateStringHandle(
                self._idInst,
                topic_name,
                CP_WINUNICODE
            )
            if not hsz_topic:
                raise DDEError("創建主題句柄失敗")
            
            # 保存主題信息
            self._topics[topic_name] = {
                "handle": hsz_topic,
                "items": {}
            }
            
            self.logger.info(f"註冊新主題: {topic_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"註冊主題失敗: {str(e)}")
            return False

    def set_item(self, topic: str, item: str, value: str) -> bool:
        """設置項目的值"""
        try:
            if topic not in self._topics:
                self.logger.warning(f"主題未註冊: {topic}")
                return False
            
            # 創建或更新項目
            if item not in self._topics[topic]["items"]:
                # 創建項目句柄
                hsz_item = DDE.CreateStringHandle(
                    self._idInst,
                    item,
                    CP_WINUNICODE
                )
                if not hsz_item:
                    raise DDEError("創建項目句柄失敗")
                
                self._topics[topic]["items"][item] = {
                    "handle": hsz_item,
                    "value": value,
                    "advise": False,  # 添加訂閱狀態
                    "clients": set()  # 添加客戶端集合
                }
            else:
                if "clients" not in self._topics[topic]["items"][item]:
                    self._topics[topic]["items"][item]["clients"] = set()
                self._topics[topic]["items"][item]["value"] = value
            
            # 通知所有訂閱者
            self.post_advise(topic, item)
            return True
            
        except Exception as e:
            self.logger.error(f"設置項目值失敗: {str(e)}")
            return False

    def get_item(self, topic: str, item: str) -> Optional[str]:
        """獲取項目的值"""
        try:
            if topic in self._topics and item in self._topics[topic]["items"]:
                return self._topics[topic]["items"][item]["value"]
            return None
        except Exception as e:
            self.logger.error(f"獲取項目值失敗: {str(e)}")
            return None

    def _callback_handler(self, uType: int, uFmt: int, hConv: HCONV,
                         hsz1: HSZ, hsz2: HSZ, hData: HDDEDATA,
                         dwData1: ULONG_PTR, dwData2: ULONG_PTR) -> HDDEDATA:
        """DDE 回調處理函數"""

        try:
            hsz1_string = DDE.query_string(self._idInst, hsz1)
        except Exception as e:
            self.logger.error(f'[錯誤] _callback_handler - query_string時發生錯誤: {str(e)}, hsz1:{hsz1}')
            pass

        try:
            hsz2_string = DDE.query_string(self._idInst, hsz2)
        except Exception as e:
            self.logger.error(f'[錯誤] _callback_handler - query_string時發生錯誤: {str(e)}, hsz2:{hsz2}')
            pass
        
        self.logger.debug(f"[S] _callback_handler - 收到 DDE 回調: 類型={uType}({hex(uType)}), 格式={uFmt}, 對話句柄={hConv}")
        self.logger.debug(f"[S] _callback_handler - 詳細信息: hsz1={hsz1}({hsz1_string}), hsz2={hsz2}({hsz2_string}), hData={hData}, dwData1={dwData1}, dwData2={dwData2}")

        try:
            if uType == XTYP_CONNECT:
                # 檢查服務名稱和主題
                topic_name = DDE.query_string(self._idInst, hsz1)
                service_name = DDE.query_string(self._idInst, hsz2)
                
                self.logger.debug(f'[S] _callback_handler - XTYP_CONNECT - service:{service_name}, topic:{topic_name}. _topics:{self._topics}')
                
                # 檢查是否為本服務器的服務名稱
                if service_name == self._service_name:
                    # 檢查主題是否存在
                    if topic_name in self._topics:
                        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                        print(f"[{timestamp}] [INFO] [S] 接受連接請求: {service_name}.{topic_name}")
                        return DDE._create_data_handle(self._idInst, None, None)
                    else:
                        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                        print(f"[{timestamp}] [WARN] [S] 拒絕連接請求: 未知主題 {topic_name}")
                        return None
                else:
                    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    print(f"[{timestamp}] [WARN] [S] 拒絕連接請求: 未知服務 {service_name}")
                    return None

            # 處理連接確認
            elif uType == XTYP_CONNECT_CONFIRM:
                self._conversations.add(hConv)
                topic_name = self._get_string(hsz2)
                self.logger.info(f"連接已確認: {topic_name}")
                return None

            elif uType == XTYP_REGISTER:
                self.logger.debug(f'[S] _callback_handler - wType == XTYP_REGISTER(0x80a2), service:{hsz1_string}')
                self.logger.debug(f'[S] _callback_handler - wType:{hex(uType)},uFmt:{uFmt}, hConv:{hConv}, hsz1:{hsz1}({hsz1_string}), hsz2:{hsz2}({hsz2_string}), hData:{hData}, dwData1:{dwData1}, dwData2:{dwData2}')

            elif uType == XTYP_ADVSTART:
                topic_name = DDE.query_string(self._idInst, hsz1)
                item_name = DDE.query_string(self._idInst, hsz2)
                
                self.logger.debug(f'[S] _callback_handler - XTYP_ADVSTART - topic_name:{topic_name}({self._topics}), item_name:{item_name}({self._topics[topic_name]["items"]})')
                try:
                    if topic_name in self._topics and item_name in self._topics[topic_name]["items"]:
                        item = self._topics[topic_name]["items"][item_name]
                        item["advise"] = True
                        item["clients"].add(hConv)
                        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                        print(f"[{timestamp}] [INFO] [S] 開始訂閱: {topic_name}.{item_name}")

                        initial_value = "--"
                        data = initial_value.encode('cp950') + b'\0'
                        _ret = DDE._create_data_handle(self._idInst, data, None)
                        self.logger.debug(f'[S] _callback_handler - XTYP_ADVSTART - _ret:{_ret}, item:{item_name}, item_clients:{item["clients"]}, topic_items:{self._topics[topic_name]["items"]}')
                        return _ret
                except Exception as e:
                    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    print(f'[{timestamp}] [ERROR] [S] 處理訂閱請求失敗: {str(e)}')
                    return None
                return None

            elif uType == XTYP_ADVREQ:
                topic_name = DDE.query_string(self._idInst, hsz1)
                item_name = DDE.query_string(self._idInst, hsz2)
                self.logger.debug(f'[S] _callback_handler - wType == XTYP_ADVREQ(0x2022) uFmt:{uFmt}, dwData1:{dwData1}, 收到數據請求: XTYP_ADVREQ - {topic_name}.{item_name}')
                self.logger.debug(f'[S] _callback_handler - wType:XTYP_ADVREQ(0x2022),uFmt:{uFmt}, hConv:{hConv}, hsz1:{hsz1}({topic_name}), hsz2:{hsz2}({item_name}), hData:{hData}, dwData1:{dwData1}, dwData2:{dwData2}')

                self.logger.debug(f"收到數據請求: XTYP_ADVREQ - {topic_name}.{item_name}")
                if (uFmt != 1):
                    self.logger.debug(f'[S] _callback_handler - wType:XTYP_ADVREQ(0x2022),Excel 特有 uFmt:{uFmt} ?')
                
                # 檢查主題和項目是否存在並返回數據
                if topic_name in self._topics and item_name in self._topics[topic_name]["items"]:
                    value = self._topics[topic_name]["items"][item_name]["value"]
                    if value:
                        data = value.encode('cp950') + b'\0'
                        hData = DDE.CreateDataHandle(
                            self._idInst,
                            data,
                            len(data),
                            0,
                            hsz2,
                            CF_TEXT,
                            0
                        )
                        return hData
                return None

            elif uType == XTYP_ADVSTOP:
                topic_name = DDE.query_string(self._idInst, hsz1)
                item_name = DDE.query_string(self._idInst, hsz2)
                
                if topic_name in self._topics and item_name in self._topics[topic_name]["items"]:
                    item = self._topics[topic_name]["items"][item_name]
                    item["advise"] = False
                    if hConv in item["clients"]:
                        item["clients"].remove(hConv)
                    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    print(f"[{timestamp}] [INFO] [S] 停止訂閱: {topic_name}.{item_name}")
                return None

            # 處理斷開連接
            elif uType == XTYP_DISCONNECT:
                if hConv in self._conversations:
                    self._conversations.remove(hConv)
                self.logger.info("客戶端斷開連接")
                return 0

            # 處理數據請求
            elif uType == XTYP_REQUEST:
                topic = DDE.query_string(self._idInst, hsz1)
                item = DDE.query_string(self._idInst, hsz2)
                self.logger.debug(f'[S] _callback_handler - XTYP_REQUEST - topic:{topic}, item:{item}')
                
                # 檢查主題和項目是否存在
                if topic in self._topics and item in self._topics[topic]["items"]:
                    value = self._topics[topic]["items"][item]["value"]
                    self.logger.debug(f'[S] _callback_handler - XTYP_REQUEST - value:{value}')
                
                    if value is not None:
                        data = str(value).encode('cp950') + b'\0'
                        hData = DDE._create_data_handle(self._idInst, data, hsz2)
                        if hData:
                            return hData
                        else:
                            self.logger.error(f"創建數據句柄失敗: {topic}.{item}")
                else:
                    self.logger.warning(f"[REQUEST] 請求的項目不存在: {topic}.{item}")
                    self.logger.debug(f'[S] _callback_handler - REQUEST - 項目不存在: {topic}.{item}')
                    self.logger.debug(f'[S] _callback_handler - REQUEST - _topics:{self._topics}')
                
                return None

            elif uType == XTYP_XACT_COMPLETE:
                self.logger.debug(f'[S] _callback_handler - XTYP_XACT_COMPLETE')
                self.logger.debug(f'[S] _callback_handler - XTYP_XACT_COMPLETE - wType:{hex(uType)},uFmt:{uFmt}, hConv:{hConv}, hsz1:{hsz1}({hsz1_string}), hsz2:{hsz2}({hsz2_string}), hData:{hData}, dwData1:{dwData1}, dwData2:{dwData2}')

                return None
            
            elif uType == XTYP_POKE:
                topic_name = DDE.query_string(self._idInst, hsz1)
                item_name = DDE.query_string(self._idInst, hsz2)
                
                self.logger.debug(f'[S] _callback_handler - POKE 請求: {topic_name}.{item_name}')
                
                # 檢查主題和項目是否存在
                if topic_name in self._topics and item_name in self._topics[topic_name]["items"]:
                    try:
                        # 獲取數據大小
                        data_size = DWORD()
                        data_ptr = DDE.AccessData(hData, byref(data_size))
                        if not data_ptr:
                            self.logger.warning('[回調:POKE] 無法訪問數據')
                            return DDE_FNOTPROCESSED
                        
                        try:
                            # 直接使用 cp950 編碼
                            value = string_at(data_ptr, data_size.value).decode('cp950').rstrip('\0')
                            
                            # 更新數據結構
                            self._topics[topic_name]["items"][item_name]["value"] = value
                            
                            self.logger.debug(f'[S] _callback_handler - 更新項目值: {topic_name}.{item_name} = {value}')
                            
                            # 調用回調函數
                            if hasattr(self, 'on_poke') and callable(self.on_poke):
                                self.on_poke(topic_name, item_name, value)
                            
                            return DDE_FACK
                            
                        finally:
                            DDE.UnaccessData(hData)
                            
                    except Exception as e:
                        self.logger.error(f'[回調:POKE] 處理數據失敗: {str(e)}')
                        return DDE_FNOTPROCESSED
                else:
                    self.logger.warning(f'[回調:POKE] 項目不存在: {topic_name}.{item_name}')
                    self.logger.warning(f'[回調:POKE] 主題: {topic_name} {"存在" if topic_name in self._topics else "不存在"}')
                    if topic_name in self._topics:
                        self.logger.warning(f'[回調:POKE] 項目列表: {list(self._topics[topic_name]["items"].keys())}')
                
                return DDE_FNOTPROCESSED
            
            elif uType == XTYP_EXECUTE:
                topic_name = DDE.query_string(self._idInst, hsz1)
                self.logger.debug(f'[S] _callback_handler - wType == XTYP_EXECUTE(0x4050) 收到命令請求: 主題={topic_name}')
                self.logger.debug(f"[EXECUTE] 收到命令請求: 主題={topic_name}")
                
                try:
                    # 獲取數據大小
                    size = DDE.GetData(hData, None, 0, 0)
                    if not size:
                        self.logger.error("[EXECUTE] 無法獲取數據大小")
                        return DDE_FNOTPROCESSED
                    
                    # 檢查命令大小限制 (設定為 1MB)
                    if size > 1024 * 1024:
                        self.logger.error(f"[EXECUTE] 命令過長: {size} 字節")
                        return DDE_FNOTPROCESSED
                    
                    # 創建緩衝區並讀取數據
                    buffer = create_string_buffer(size)
                    result = DDE.GetData(hData, buffer, size, 0)
                    if result != size:
                        self.logger.error(f"[EXECUTE] 讀取數據失敗: 預期 {size} 字節，實際讀取 {result} 字節")
                        return DDE_FNOTPROCESSED
                    
                    # 獲取原始數據
                    raw_bytes = buffer.raw
                    
                    try:
                        # 嘗試使用 UTF-16-LE 解碼
                        command = raw_bytes.decode('utf-16-le').split('\x00')[0]
                        self.logger.debug(f"[EXECUTE] UTF-16-LE 解碼成功: {command!r}")
                    except UnicodeDecodeError:
                        try:
                            # 如果 UTF-16-LE 解碼失敗，嘗試使用 CP950
                            command = raw_bytes.split(b'\x00')[0].decode('cp950')
                            self.logger.debug(f"[EXECUTE] CP950 解碼成功: {command!r}")
                        except UnicodeDecodeError as e:
                            self.logger.error(f"[EXECUTE] 解碼失敗: {str(e)}")
                            return DDE_FNOTPROCESSED
                    
                    # 移除控制字符和空白
                    command = ''.join(char for char in command if ord(char) >= 0x20 and ord(char) <= 0xFFFF)
                    command = command.strip()
                    
                    # 基本命令驗證
                    if not command:
                        self.logger.error("[EXECUTE] 命令為空")
                        return DDE_FNOTPROCESSED
                    
                    if len(command) > 1000:  # 設定合理的命令長度限制
                        self.logger.error(f"[EXECUTE] 命令長度超過限制: {len(command)} 字符")
                        return DDE_FNOTPROCESSED
                    
                    # 移除 [Stock] 前綴
                    if command.startswith('[Stock]'):
                        command = command[7:].lstrip()
                    
                    # 如果有註冊 execute 回調，則調用
                    if hasattr(self, 'on_execute') and callable(self.on_execute):
                        try:
                            result = self.on_execute(topic_name, command)
                            if result:
                                self.logger.info(f"[EXECUTE] 執行命令: [{topic_name}] {command}")
                                return DDE_FACK
                            else:
                                self.logger.warning(f"[EXECUTE] 命令執行失敗: {command!r}")
                        except Exception as e:
                            self.logger.error(f"[EXECUTE] 執行異常: {str(e)}")
                    else:
                        self.logger.warning("[EXECUTE] 未註冊命令處理回調")
                    
                except Exception as e:
                    self.logger.error(f"[EXECUTE] 處理失敗: {str(e)}")
                
                return DDE_FNOTPROCESSED
            
            elif (uType == WM_DDE_DATA or uType == WM_DDE_ACK or uType == WM_DDE_ADVISE):
                self.logger.debug(f'[S] _callback_handler - wType == WM_DDE_???({hex(uType)})')


            else:
                if (hex(uType)=="xxx"):
                    uTypeStr = "XTYP_xxx"
                elif (hex(uType)=="xxxx"):
                    uTypeStr = "XTYP_xxxx"
                else:
                    uTypeStr = "unKnown"
                self.logger.debug(f'[S] _callback_handler - wType:{hex(uType)} - {uTypeStr}')
                return None
                
            #return None
            return 0
            
        except Exception as e:
            self.logger.error(f"處理回調時發生錯誤: {str(e)}")
            return 0

    def _get_string(self, hsz):
        """獲取字符串"""
        if hsz:
            return DDE.query_string(self._idInst, hsz)
        return ""

    def _create_dde_data(self, data: bytes) -> HDDEDATA:
        """創建 DDE 數據句柄
        
        Args:
            data: 要傳送的數據
        
        Returns:
            HDDEDATA: 數據句柄，失敗時返回 None
        
        參考:
            https://learn.microsoft.com/en-us/windows/win32/api/ddeml/nf-ddeml-ddecreatedatahandle
        """
        try:
            return DDE.CreateDataHandle(
                self._idInst,     # idInst: 實例 ID
                data,            # pSrc: 源數據
                len(data),       # cb: 數據長度(包含結尾 null)
                0,              # cbOff: 偏移量
                None,           # hszItem: 可選的項目句柄
                CF_TEXT,        # wFmt: 數據格式
                HDATA_APPOWNED  # afCmd: 應用程序擁有數據
            )
        except Exception as e:
            self.logger.error(f"創建數據句柄失敗: {str(e)}")
            return None

    def post_advise(self, topic_name: str, item_name: str) -> None:
        """推送更新給訂閱者
        
        Args:
            topic_name: 主題名稱
            item_name: 項目名稱
        """
        try:
            if topic_name not in self._topics:
                return
                
            topic = self._topics[topic_name]
            if item_name not in topic["items"]:
                return
                
            item = topic["items"][item_name]
            value = item["value"]
            
            # 創建數據句柄
            data = str(value).encode('cp950') + b'\0'  # 確保數據以 null 結尾
            hData = self._create_dde_data(data)
            
            if hData:
                try:
                    # 推送給所有訂閱者
                    result = DDE.PostAdvise(
                        self._idInst,        # idInst
                        topic["handle"],      # hszTopic
                        item["handle"]        # hszItem
                    )
                    
                    if not result:
                        error_code = DDE.GetLastError()
                        self.logger.error(f"PostAdvise 失敗，錯誤碼: {error_code}")
                        
                finally:
                    DDE.FreeDataHandle(hData)
                        
        except Exception as e:
            self.logger.error(f"推送更新失敗: {str(e)}")

    def register_poke_callback(self, callback: Callable[[str, str, str], None]) -> None:
        """註冊 POKE 回調函數
        
        Args:
            callback: 回調函數，接收 topic_name, item_name, value 三個參數
        """
        self.on_poke = callback
        
    def register_execute_callback(self, callback: Callable[[str, str], bool]) -> None:
        """註冊 EXECUTE 回調函數
        
        Args:
            callback: 回調函數，接收 topic_name, command 兩個參數，
                     返回 True 表示執行成功，False 表示執行失敗
        """
        self.on_execute = callback

    def _validate_data(self, data: str, max_length: int = 1024 * 1024) -> bool:
        """驗證數據是否合法"""
        if not data:
            return False
        if len(data) > max_length:
            return False
        # 可以添加更多驗證邏輯
        return True

    def _validate_topic_name(self, topic_name: str) -> bool:
        """驗證主題名稱(內部方法)"""
        return bool(topic_name and len(topic_name) <= 255)

    def _validate_item_name(self, item_name: str) -> bool:
        """驗證項目名稱(內部方法)"""
        return bool(item_name and len(item_name) <= 255)

    def _has_item_permission(self, topic_name: str, item_name: str) -> bool:
        """檢查項目權限(內部方法)"""
        return (topic_name in self._topics and 
                item_name in self._topics[topic_name]["items"])

    def __del__(self):
        """清理資源"""
        try:
            # 釋放所有主題和項目句柄
            for topic_name, topic in self._topics.items():
                # 釋放項目句柄
                for item_name, item in topic["items"].items():
                    if "handle" in item:
                        DDE.FreeStringHandle(self._idInst, item["handle"])
                
                # 釋放主題句柄
                if "handle" in topic:
                    DDE.FreeStringHandle(self._idInst, topic["handle"])
             
            # 釋放服務名稱句柄
            if hasattr(self, '_hsz_service'):
                DDE.FreeStringHandle(self._idInst, self._hsz_service)
            
            # 取消註冊服務
            if self._initialized:
                DDE.NameService(self._idInst, self._hsz_service, 0, DNS_UNREGISTER)
                DDE.Uninitialize(self._idInst)
             
        except Exception as e:
            self.logger.error(f'[清理] 發生錯誤: {str(e)}')

    def update_item(self, topic: str, item: str, value: str) -> bool:
        """更新項目值並通知所有訂閱的客戶端"""
        try:
            if not self._initialized:
                self.logger.error("服務器未初始化")
                return False
                
            if topic not in self._topics:
                self.logger.error(f"主題不存在: {topic}")
                return False
                
            # 設置項目值
            result = self.set_item(topic, item, value)
            if not result:
                return False
                
            # 通知已經由 set_item 中的 post_advise 處理
            return True
            
        except Exception as e:
            self.logger.error(f"更新項目時出錯: {str(e)}")
            return False

    def _handle_advstart(self, hConv: HCONV, topic_name: str, item_name: str) -> bool:
        """處理訂閱請求"""
        try:
            # 檢查主題和項目是否存在
            if (topic_name in self._topics and 
                item_name in self._topics[topic_name]["items"]):
                
                # 記錄訂閱（如果需要的話）
                self.logger.info(f"[訂閱] 新增訂閱: {topic_name}.{item_name}")
                return True
                
            return False
            
        except Exception as e:
            self.logger.error(f"處理訂閱請求失敗: {str(e)}")
            return False

    def register_item(self, topic: str, item: str, initial_value: str = "--") -> bool:
        """註冊項目並設定初始值"""
        try:
            if topic not in self._topics:
                self.logger.warning(f"主題未註冊: {topic}")
                return False
                
            # 創建項目句柄
            hsz_item = DDE.CreateStringHandle(
                self._idInst,
                item,
                CP_WINUNICODE
            )
            if not hsz_item:
                raise DDEError("創建項目句柄失敗")
            
            # 保存項目信息並設定初始值
            self._topics[topic]["items"][item] = {
                "handle": hsz_item,
                "value": initial_value
            }
            
            self.logger.info(f"註冊項目成功: {topic}.{item} = {initial_value}")
            return True
            
        except Exception as e:
            self.logger.error(f"註冊項目失敗: {str(e)}")
            return False

    def disconnect(self) -> None:
        """斷開 DDE 服務器連接"""
        try:
            # 釋放所有主題和項目的句柄
            for topic_name, topic in self._topics.items():
                # 釋放項目句柄
                for item_name, item in topic["items"].items():
                    if "handle" in item:
                        DDE.FreeStringHandle(self._idInst, item["handle"])
                
                # 釋放主題句柄
                if "handle" in topic:
                    DDE.FreeStringHandle(self._idInst, topic["handle"])
            
            # 釋放服務名稱句柄
            if hasattr(self, '_hsz_service'):
                DDE.FreeStringHandle(self._idInst, self._hsz_service)
            
            # 取消註冊服務器
            if self._idInst:
                DDE.NameService(self._idInst, self._hsz_service, 0, DNS_UNREGISTER)
                
                # 終止 DDE 實例
                DDE.Uninitialize(self._idInst)
            
            # 清理資源
            self._topics.clear()
            self._idInst = None
            self._hsz_service = None
            self._initialized = False
            
        except Exception as e:
            self.logger.error(f"斷開服務器連接失敗: {str(e)}")

    def _handle_disconnect(self, reason: str = "未知原因"):
        """處理 DDE 服務器斷開連接"""
        self.logger.warning(f"DDE 服務器斷開連接: {reason}")
        self._connected = False
        
        # 如果啟用自動重連，則嘗試重新連接
        if self._auto_reconnect:
            if self.reconnect():
                # 重新訂閱之前的項目
                for item_name in self._callbacks.keys():
                    self.advise(item_name, self._callbacks[item_name])
        return DDE_FNOTPROCESSED
                
def run_message_loop(stop_event: Optional[Event] = None):
    """運行 Windows 消息循環"""
    msg = MSG()
    while not (stop_event and stop_event.is_set()):
        while DDE.PeekMessage(byref(msg), None, 0, 0, PM_REMOVE):
            DDE.TranslateMessage(byref(msg))
            DDE.DispatchMessage(byref(msg))
        time.sleep(0.001)  # 避 CPU 使用過高

# 更 __all__
__all__ = [
    'DDEClient',
    'DDEServer',
    'DDEError',
    'DDEClientError',
    'DDEServerError',
    'run_message_loop'
]

# 版本信息
__version__ = '0.1.0'

class DDECache:
    """DDE 數據緩存"""
    def __init__(self, max_size: int = 1000, ttl: int = 300):
        self._cache = {}
        self._max_size = max_size
        self._ttl = timedelta(seconds=ttl)

    def set(self, key: str, value: Any) -> None:
        """設置緩存"""
        if len(self._cache) >= self._max_size:
            # 清理過期項目
            self._cleanup()
        self._cache[key] = {
            'value': value,
            'timestamp': datetime.now()
        }

    def get(self, key: str) -> Optional[Any]:
        """獲取緩存"""
        if key in self._cache:
            item = self._cache[key]
            if datetime.now() - item['timestamp'] < self._ttl:
                return item['value']
            del self._cache[key]
        return None

    def _cleanup(self) -> None:
        """清理過期緩存"""
        now = datetime.now()
        expired_keys = [
            k for k, v in self._cache.items()
            if now - v['timestamp'] >= self._ttl
        ]
        for k in expired_keys:
            del self._cache[k]

# DDE 常量定義
HDATA_APPOWNED = 0x0001  # 應用程序擁有數據句柄

def timestamp() -> str:
    """返回格式化的時間戳"""
    return datetime.now().strftime("%Y-%m-%d %H:%M:%S")
